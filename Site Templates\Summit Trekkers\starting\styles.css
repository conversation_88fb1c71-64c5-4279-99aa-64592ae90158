/* Summit Trekkers - Hiking & Outdoor Adventure Club Styles */
:root {
  --primary-color: #3b82f6; /* Blue */
  --primary-dark: #2563eb;
  --primary-light: #93c5fd;
  --secondary-color: #10b981; /* Green */
  --secondary-dark: #059669;
  --accent-color: #f59e0b; /* Amber */
  --text-color: #1e293b;
  --text-light: #64748b;
  --white: #ffffff;
  --light-bg: #f8fafc;
  --gray-bg: #f1f5f9;
  --dark-bg: #0f172a;
  --border-radius: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
  
  /* Difficulty Colors */
  --beginner-color: #10b981;
  --intermediate-color: #f59e0b;
  --advanced-color: #ef4444;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Nunito', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  background-color: var(--white);
  font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Roboto Slab', serif;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-color);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--primary-color);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-header p {
  max-width: 600px;
  margin: 15px auto 0;
  color: var(--text-light);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: 'Nunito', sans-serif;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--text-light);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-3px);
}

.btn-block {
  display: block;
  width: 100%;
}

/* Difficulty Badges */
.difficulty {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.difficulty.beginner {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--beginner-color);
}

.difficulty.intermediate {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--intermediate-color);
}

.difficulty.advanced {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--advanced-color);
}

/* Header */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-family: 'Roboto Slab', serif;
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--text-color);
}

.logo i {
  color: var(--primary-color);
  margin-right: 8px;
  font-size: 1.8rem;
}

.main-nav ul {
  display: flex;
  list-style: none;
}

.main-nav li {
  margin: 0 15px;
}

.main-nav a {
  color: var(--text-color);
  font-weight: 600;
  position: relative;
  padding: 5px 0;
}

.main-nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav a:hover::after {
  width: 100%;
}

.main-nav a.btn {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 8px 16px;
  border-radius: var(--border-radius);
}

.main-nav a.btn:hover {
  background-color: var(--primary-dark);
}

.main-nav a.btn::after {
  display: none;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 24px;
  position: relative;
  z-index: 1001;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-color);
  position: absolute;
  left: 0;
  transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
  top: 0;
}

.mobile-menu-toggle span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
  bottom: 0;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 11px;
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 11px;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  min-height: 700px;
  background-image: url('https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  color: var(--white);
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.hero-section h1 {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  color: var(--white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.hero-section .btn-outline {
  color: var(--white);
  border-color: var(--white);
}

.hero-section .btn-outline:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.7));
}

/* About Section */
.about-section {
  padding: 100px 0;
  background-color: var(--white);
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-text {
  flex: 1;
}

.about-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.feature {
  text-align: center;
}

.feature i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.feature h3 {
  margin-bottom: 10px;
}

.feature p {
  color: var(--text-light);
  margin-bottom: 0;
  font-size: 0.95rem;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Adventures Section */
.adventures-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.adventures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.adventure-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.adventure-card:hover {
  transform: translateY(-10px);
}

.adventure-image {
  position: relative;
  height: 200px;
}

.adventure-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.adventure-image .difficulty {
  position: absolute;
  top: 15px;
  right: 15px;
}

.adventure-content {
  padding: 25px;
}

.adventure-content h3 {
  margin-bottom: 15px;
}

.adventure-content p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.adventure-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.adventure-details span {
  font-size: 0.9rem;
  color: var(--text-light);
}

.adventure-details i {
  color: var(--primary-color);
  margin-right: 5px;
}

/* Testimonial Section */
.testimonial-section {
  padding: 100px 0;
  background-color: var(--primary-color);
  color: var(--white);
}

.testimonial {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
}

.quote-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 20px;
}

.testimonial blockquote {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.testimonial cite {
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial cite img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  border: 3px solid var(--white);
}

.testimonial cite div {
  text-align: left;
}

.testimonial cite strong {
  display: block;
  font-size: 1.1rem;
  font-style: normal;
}

.testimonial cite span {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Schedule Section */
.schedule-section {
  padding: 100px 0;
  background-color: var(--white);
}

.schedule-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.schedule-card {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  display: flex;
}

.schedule-date {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.schedule-date .day {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.schedule-date .month {
  font-size: 1rem;
  font-weight: 600;
}

.schedule-content {
  padding: 25px;
  flex: 1;
}

.schedule-content .difficulty {
  margin-bottom: 10px;
}

.schedule-content h3 {
  margin-bottom: 10px;
}

.schedule-content p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.schedule-details span {
  font-size: 0.9rem;
  color: var(--text-light);
}

.schedule-details i {
  color: var(--primary-color);
  margin-right: 5px;
  width: 20px;
  text-align: center;
}

/* Gallery Section */
.gallery-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.gallery-item {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  height: 250px;
  transition: var(--transition);
}

.gallery-item:hover {
  transform: scale(1.03);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

/* Join Section */
.join-section {
  padding: 100px 0;
  background-color: var(--white);
}

.join-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.join-text {
  padding-right: 20px;
}

.join-benefits {
  margin-top: 30px;
}

.benefit {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.benefit i {
  color: var(--secondary-color);
  margin-right: 10px;
  font-size: 1.2rem;
}

.join-form {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: 'Nunito', sans-serif;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Partner Section */
.partner-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.partner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.partner-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.partner-card:hover {
  transform: translateY(-10px);
}

.partner-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.partner-card h3 {
  padding: 20px 20px 10px;
}

.partner-card p {
  padding: 0 20px;
  color: var(--text-light);
  margin-bottom: 20px;
}

.partner-details {
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.partner-details span {
  font-size: 0.9rem;
  color: var(--text-light);
}

.partner-details i {
  color: var(--primary-color);
  margin-right: 5px;
  width: 20px;
  text-align: center;
}

/* Footer */
.site-footer {
  background-color: var(--dark-bg);
  color: var(--white);
  padding: 80px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-info {
  padding-right: 20px;
}

.footer-info .logo {
  color: var(--white);
  margin-bottom: 15px;
}

.footer-info p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-radius: 50%;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-newsletter h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-newsletter h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-newsletter p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  font-family: inherit;
  font-size: 0.95rem;
}

.newsletter-form button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0 20px;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background-color: var(--primary-dark);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  h2 {
    font-size: 2.2rem;
  }
  
  .hero-section h1 {
    font-size: 3.5rem;
  }
  
  .about-content,
  .join-content {
    gap: 40px;
  }
  
  .about-features {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .hero-section h1 {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .main-nav {
    display: none;
  }
  
  .main-nav.active {
    display: block;
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .main-nav.active ul {
    flex-direction: column;
  }
  
  .main-nav.active li {
    margin: 10px 0;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .about-content,
  .join-content {
    grid-template-columns: 1fr;
  }
  
  .about-features {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .about-image {
    order: -1;
    margin-bottom: 30px;
  }
  
  .schedule-card {
    flex-direction: column;
  }
  
  .schedule-date {
    flex-direction: row;
    padding: 15px;
    justify-content: flex-start;
  }
  
  .schedule-date .day {
    margin-right: 5px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-links h3::after,
  .footer-newsletter h3::after {
    left: 0;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
  }
  
  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 15px;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .about-section,
  .adventures-section,
  .testimonial-section,
  .schedule-section,
  .gallery-section,
  .join-section,
  .partner-section {
    padding: 60px 0;
  }
  
  .testimonial blockquote {
    font-size: 1.2rem;
  }
  
  .join-form {
    padding: 30px 20px;
  }
}
