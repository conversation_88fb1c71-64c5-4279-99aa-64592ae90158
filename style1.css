@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    /* Restore original variable names */
    --primary-color: #111827;    /* Dark gray-blue */
    --secondary-color: #1F2937;  /* Darker gray-blue */
    --accent-color: #6366F1;     /* Indigo */
    --highlight-color: #818CF8;  /* Light indigo */
    --muted-color: #9CA3AF;      /* Gray */
    --light-color: #F9FAFB;      /* Off white */
    --text-light: #F3F4F6;       /* Light gray */
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-color: rgba(156, 163, 175, 0.2); /* Restore border */
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* Restore shadow */

    --transition-speed: 0.3s;
    --header-height: 64px;
    --border-radius: 12px;
    /* Removed theme-transition */
}

/* Removed body.light-theme rule */

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-color);
    color: var(--light-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    letter-spacing: 0.01em;
    /* Removed transition: var(--theme-transition); */
}

#navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--header-height);
    background-color: rgba(31, 41, 55, 0.95); /* Restore original semi-transparent */
    padding: 1rem 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px); /* Modern blur effect */
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px var(--shadow-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transition: all var(--transition-speed) ease; /* Remove theme transition */
}

#navbar-logo {
    width: 60px;
    height: auto;
    transition: transform 0.3s ease;
}

#navbar-logo:hover {
    transform: scale(1.05);
}

#navbar > div {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

#navbar ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

#navbar li {
    display: flex;
    gap: 2rem;
}

#navbar a {
    color: var(--light-color);
    text-decoration: none;
    transition: all 0.3s ease; /* Remove theme transition */
    padding: 0.75rem 1.25rem;
    position: relative;
    font-weight: 500;
}

#navbar a:hover {
    color: var(--accent-color);
}

#navbar a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

#navbar a:hover::after {
    width: 70%;
}

main {
    padding-top: 100px; /* Add padding to account for fixed navbar */
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
    padding-left: 2rem;
    padding-right: 2rem;
}

main h1 {
    font-size: 2.5rem;
    color: var(--accent-color);
    text-align: center;
    margin-bottom: 2rem;
    padding-top: 1rem;
    position: relative;
    z-index: 1;
    /* Removed transition: var(--theme-transition); */
}

main p {
    text-align: center;
    color: var(--muted-color);
    max-width: 800px;
    margin: 0 auto 2rem;
    /* Removed transition: var(--theme-transition); */
}

.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.gallery-item {
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease; /* Remove theme transition */
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.05); /* Restore original border */
    position: relative;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(99, 102, 241, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-bottom: 2px solid var(--accent-color);
    transition: all 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-button {
    width: 100%;
    padding: 1rem;
    background-color: var(--accent-color);
    color: var(--light-color);
    border: none;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease; /* Remove theme transition */
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.gallery-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: var(--highlight-color);
    transition: all 0.5s ease; /* Remove theme transition */
    z-index: -1;
}

.gallery-button:hover {
    letter-spacing: 1px;
}

.gallery-button:hover::before {
    width: 100%;
}

footer {
    background-color: var(--secondary-color);
    padding: 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    /* Removed transition: var(--theme-transition); */
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    opacity: 0.5;
}

footer div {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

#footer-logo {
    width: 60px;
    height: auto;
}

footer p {
    color: #fff;
    text-align: center;
}

@media (max-width: 768px) {
    #navbar {
        padding: 0.5rem 0;
    }

    #navbar > div {
        flex-direction: row;
    }

    #navbar ul {
        flex-direction: row;
    }

    #navbar li {
        flex-direction: row;
    }

    #navbar a {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .gallery {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 2rem;
    }

    footer div {
        flex-direction: column;
        gap: 1.5rem;
    }

    main {
        padding-top: 180px; /* Adjusted padding for mobile */
    }

    main h1 {
        font-size: 2rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        @apply transition-none;
    }
}

/* Print styles */
@media print {
    .gallery-item {
        @apply break-inside-avoid;
    }
}