/* Dream Weddings - Modern Elegant Theme */
:root {
    --primary-color: #d8b4e2; /* Soft lavender */
    --primary-dark: #b68fc0; /* Darker lavender */
    --secondary-color: #f5e6fa; /* Light lavender */
    --accent-color: #8ba287; /* Sage green */
    --accent-dark: #718069; /* Darker sage */
    --text-color: #333333;
    --text-light: #666666;
    --white: #ffffff;
    --light-bg: #f9f7fa;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Montserrat', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-bg);
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.2rem;
}

h3 {
    font-size: 1.75rem;
}

p {
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.text-center {
    text-align: center;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 28px;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0.5px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-light {
    background-color: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--white);
}

.btn-light:hover {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
    transform: translateY(-3px);
}

.btn-outline {
    background-color: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
}

.btn-outline:hover {
    background-color: var(--accent-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-block {
    display: block;
    width: 100%;
}

/* Header */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    padding: 15px 0;
    transition: var(--transition);
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    flex: 0 0 auto;
}

.logo {
    max-height: 60px;
    width: auto;
}

.main-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav li {
    margin: 0 20px;
}

.main-nav a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 0;
    position: relative;
    transition: var(--transition);
}

.main-nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.header-cta {
    margin-left: 20px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
}

.hamburger {
    display: block;
    position: relative;
    width: 24px;
    height: 2px;
    background-color: var(--text-color);
    transition: var(--transition);
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 2px;
    background-color: var(--text-color);
    transition: var(--transition);
}

.hamburger::before {
    top: -8px;
}

.hamburger::after {
    bottom: -8px;
}

.mobile-menu-toggle.active .hamburger {
    background-color: transparent;
}

.mobile-menu-toggle.active .hamburger::before {
    transform: rotate(45deg);
    top: 0;
}

.mobile-menu-toggle.active .hamburger::after {
    transform: rotate(-45deg);
    bottom: 0;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 90vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    color: var(--white);
    margin-top: 0;
    overflow: hidden;
}

.hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
}

.hero-content {
    text-align: center;
    max-width: 800px;
    padding-top: 80px; /* Account for fixed header */
    z-index: 1;
}

.hero-content h1 {
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.divider {
    position: relative;
    height: 1px;
    width: 80px;
    background-color: var(--accent-color);
    margin: 20px auto 30px;
}

.divider span {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--accent-color);
    border-radius: 50%;
    top: -4.5px;
    left: 50%;
    transform: translateX(-50%);
}

/* About Section */
.about-section {
    background-color: var(--white);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.about-image {
    flex: 0 0 45%;
}

.about-text {
    flex: 1;
}

.rounded-image {
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    width: 100%;
    height: auto;
    transition: var(--transition);
}

.rounded-image:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Services Section */
.services-section {
    background-color: var(--secondary-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.service-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-icon {
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 32px;
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

.service-card p {
    color: var(--text-light);
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Vision Section */
.vision-section {
    background-color: var(--white);
}

.vision-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.vision-text {
    flex: 1;
}

.vision-image {
    flex: 0 0 45%;
}

/* Testimonials Section */
.testimonials-section {
    background-color: var(--secondary-color);
    text-align: center;
}

.testimonial-slider {
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--box-shadow);
    position: relative;
}

.quote-icon {
    font-size: 30px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

blockquote {
    font-family: 'Cormorant Garamond', serif;
    font-size: 1.2rem;
    line-height: 1.8;
    font-style: italic;
    color: var(--text-color);
    margin-bottom: 30px;
}

.testimonial-author {
    margin-top: 20px;
}

.author-info h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.author-info p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Consultation Section */
.consultation-section {
    background-color: var(--white);
}

.consultation-form {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--light-bg);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(216, 180, 226, 0.2);
}

/* Footer */
.site-footer {
    background-color: var(--text-color);
    color: var(--white);
    padding: 80px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.footer-logo img {
    max-width: 150px;
    margin-bottom: 20px;
}

.footer-logo p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0;
}

.footer-links h3,
.footer-contact h3,
.footer-social h3 {
    color: var(--white);
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

.footer-links h3::after,
.footer-contact h3::after,
.footer-social h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
}

.footer-contact i {
    margin-right: 10px;
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Responsive Styles */
@media (max-width: 992px) {
    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.2rem;
    }

    .about-content,
    .vision-content {
        flex-direction: column;
        gap: 40px;
    }

    .about-image,
    .vision-image {
        flex: 0 0 100%;
        order: -1;
    }

    .about-text,
    .vision-text {
        text-align: center;
    }

    section {
        padding: 80px 0;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .main-nav {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--white);
        flex-direction: column;
        padding: 100px 40px 40px;
        transition: right 0.3s ease;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    }

    .main-nav.active {
        right: 0;
    }

    .main-nav ul {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-nav li {
        margin: 15px 0;
        width: 100%;
    }

    .main-nav a {
        display: block;
        font-size: 1.2rem;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .header-cta {
        display: none;
    }

    .hero-section {
        height: 80vh;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links h3::after,
    .footer-contact h3::after,
    .footer-social h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-contact p {
        justify-content: center;
    }

    .social-icons {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .hero-section {
        height: 70vh;
    }

    .consultation-form {
        padding: 30px 20px;
    }

    section {
        padding: 60px 0;
    }
}

