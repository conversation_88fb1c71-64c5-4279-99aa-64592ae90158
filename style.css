@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #111827;    /* Dark gray-blue */
    --secondary-color: #1F2937;  /* Darker gray-blue */
    --accent-color: #6366F1;     /* Indigo */
    --highlight-color: #818CF8;  /* Light indigo */
    --muted-color: #9CA3AF;      /* Gray */
    --light-color: #F9FAFB;      /* Off white */
    --text-light: #F3F4F6;       /* Light gray */
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --header-height: 64px;       /* 4rem/16px header height */
    --border-radius: 12px;       /* Consistent border radius */
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    @apply bg-primary text-light min-h-screen flex flex-col;
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-color);
    color: var(--light-color);
    line-height: 1.6;
    letter-spacing: 0.01em;
}

#footer-logo {
    @apply w-24 mx-auto;
}

#logo {
    width: 15%;
    height: auto;
    border-radius: 10%;
}

#navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--header-height);
    background-color: rgba(31, 41, 55, 0.95); /* Semi-transparent background */
    padding: 1rem 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px); /* Modern blur effect */
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px var(--shadow-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transition: all var(--transition-speed) ease;
}

#navbar > div {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

#navbar-logo {
    width: 60px;
    height: auto;
    transition: transform 0.3s ease;
}

#navbar-logo:hover {
    transform: scale(1.05);
}

#navbar ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

#navbar li {
    display: flex;
    gap: 2rem;
}

#navbar a {
    color: var(--light-color);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    position: relative;
    font-weight: 500;
}

#navbar a:hover {
    color: var(--accent-color);
}

#navbar a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

#navbar a:hover::after {
    width: 70%;
}

main {
    padding-top: 100px; /* Further increased padding to ensure content is below header */
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
    padding-left: 2rem;
    padding-right: 2rem;
}

main h1 {
    font-size: 2.5rem;
    color: var(--accent-color);
    text-align: center;
    margin-bottom: 2rem;
    padding-top: 1rem;
    position: relative;
    z-index: 1;
}

.welcome-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    align-items: center;
    padding: 2rem 0;
}

.welcome-intro {
    text-align: center;
    padding: 2rem;
    background-color: var(--secondary-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.welcome-intro h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.image-container img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    transition: transform var(--transition-speed) ease;
}

#welcome-img, #content-img {
    width: 100%;
    max-width: 400px;
    border-radius: 10px;
    transition: transform var(--transition-speed) ease;
}

.content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem 0;
    margin-top: 2rem;
    border-top: 1px solid var(--text-light);
}

button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: var(--highlight-color);
    transition: all 0.5s ease;
    z-index: -1;
}

button:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

button:hover::before {
    width: 100%;
}

footer {
    background-color: var(--secondary-color);
    padding: 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    opacity: 0.5;
}

footer div {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

#footer-logo {
    width: 60px;
    height: auto;
    margin: 0 1rem;
}

footer p {
    color: #fff;
    text-align: center;
}

/* Media Queries */
@media (max-width: 1024px) {
    .welcome-section,
    .content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    #navbar {
        padding: 0.5rem 0;
    }

    #navbar > div {
        flex-direction: row;
    }

    #navbar ul {
        flex-direction: row;
    }

    #navbar li {
        flex-direction: row;
    }

    #navbar a {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    footer div {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    #footer-logo {
        width: 50px;
    }

    .welcome-section,
    .content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    main {
        padding-top: 180px; /* Adjusted padding for mobile */
    }

    main h1 {
        padding-top: 2rem;
    }

    .welcome-intro {
        padding: 1.5rem;
    }

    #welcome-img,
    #content-img {
        max-width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

.animate-fadeIn {
    animation: fadeIn 1s ease forwards;
}

.animate-slideUp {
    animation: slideUp 0.8s ease forwards;
}

.animate-slideInLeft {
    animation: slideInLeft 0.8s ease forwards;
}

.animate-slideInRight {
    animation: slideInRight 0.8s ease forwards;
}

.animation-delay-200 {
    animation-delay: 0.2s;
}

.animation-delay-400 {
    animation-delay: 0.4s;
}

.animation-delay-600 {
    animation-delay: 0.6s;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Add Tailwind utility classes for common patterns */
.btn {
    @apply px-4 py-2 rounded-lg transition-all duration-300;
}

.btn-primary {
    @apply bg-accent text-light hover:bg-highlight;
}

.card {
    @apply bg-secondary rounded-lg shadow-lg p-6;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.card img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 8px;
}

.gallery-item {
    background: var(--secondary-color);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--card-shadow);
    position: relative;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(99, 102, 241, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 2px solid var(--accent-color);
    transition: all 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-button {
    background-color: var(--accent-color);
    width: 100%;
    padding: 1rem;
    color: var(--light-color);
    border: none;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.gallery-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: var(--highlight-color);
    transition: all 0.5s ease;
    z-index: -1;
}

.gallery-button:hover {
    letter-spacing: 1px;
}

.gallery-button:hover::before {
    width: 100%;
}

/* Container for the section */
.section-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
}

/* Card styling */
.card {
    background-color: var(--secondary-color);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    padding: 1.5rem;
    text-align: center;
    color: var(--light-color);
}

/* Image styling */
.card img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Text styling */
.card h2 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

.card p {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .section-container {
        flex-direction: column;
        padding: 1rem;
    }

    .card {
        max-width: 100%;
    }
}