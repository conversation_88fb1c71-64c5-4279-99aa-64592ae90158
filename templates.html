<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional website templates from BytesByBlinken Media. Premium, responsive templates for businesses, portfolios, and e-commerce. Custom web design agency quality at template prices.">
    <meta name="keywords" content="website templates, professional web templates, responsive templates, business website templates, custom web design templates, premium HTML templates, web design agency templates" />
    <meta name="author" content="BytesByBlinken">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta property="og:title" content="Website Templates - BytesByBlinken">
    <meta property="og:description" content="Professional website templates for your next project">
    <meta property="og:image" content="https://builtbyblinken.com/images/Logo.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="https://builtbyblinken.com/templates.html">
    <meta property="og:type" content="website">
    <link rel="canonical" href="https://builtbyblinken.com/templates.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#111827',    // Dark gray-blue
                        secondary: '#1F2937',  // Darker gray-blue
                        accent: '#6366F1',     // Indigo
                        highlight: '#818CF8',  // Light indigo
                        muted: '#9CA3AF',      // Gray
                        light: '#F9FAFB'       // Off white
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="style1.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" type="image/png" href="./images/Logo.png">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9R77YHRG17"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-9R77YHRG17');
    </script>

    <!-- Structured Data for Templates Page -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": "Website Templates - BytesByBlinken Media",
      "description": "Professional website templates from a leading custom web design agency. Premium, responsive templates for businesses and portfolios.",
      "url": "https://builtbyblinken.com/templates.html",
      "isPartOf": {
        "@type": "WebSite",
        "name": "BytesByBlinken Media",
        "url": "https://builtbyblinken.com"
      },
      "mainEntity": {
        "@type": "ItemList",
        "name": "Website Template Collection",
        "description": "Professional website templates for various industries",
        "numberOfItems": "20+"
      }
    }
    </script>
    <style>
        .template-card {
            transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
            opacity: 1;
            transform: scale(1);
        }

        .filter-btn {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
    </style>
    <title>Website Templates - BytesByBlinken</title>
</head>
<body class="bg-primary text-light min-h-screen flex flex-col">
    <header>
        <nav id="navbar" class="fixed top-0 left-0 w-full bg-secondary/95 backdrop-blur-md border-b border-muted/20 shadow-lg z-50 transition-all duration-300">
            <div class="container mx-auto px-4 py-3 flex flex-row justify-between items-center">
                <a href="./index.html" class="transition-transform duration-300 hover:scale-105 flex items-center">
                    <img id="navbar-logo" src="./images/Logo.png" alt="BytesByBlinken Logo" class="w-10 h-auto">
                </a>
                <ul class="flex flex-row space-x-8 items-center">
                    <li class="flex flex-row md:space-x-8 space-x-4">
                        <a href="./index.html" class="text-light hover:text-accent transition-colors relative py-2 flex items-center after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-0 after:h-0.5 after:bg-accent after:transition-all hover:after:w-4/5">Home</a>
                        <a href="./templates.html" class="text-accent transition-colors relative py-2 flex items-center after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-4/5 after:h-0.5 after:bg-accent after:transition-all">Templates</a>
                        <a href="./pricing.html" class="text-light hover:text-accent transition-colors relative py-2 flex items-center after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-0 after:h-0.5 after:bg-accent after:transition-all hover:after:w-4/5">Pricing</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>
    <main class="container mx-auto px-4 pt-28 flex-grow">
        <div class="text-center max-w-3xl mx-auto">
            <h1 class="text-4xl font-bold text-accent mb-6 relative inline-block">
                <span class="relative z-10">Professional Website Templates</span>
                <span class="absolute -bottom-2 left-0 w-24 h-1 bg-accent opacity-70"></span>
            </h1>
            <p class="text-muted max-w-2xl mx-auto mb-12">Browse our collection of responsive, modern templates designed for various industries and purposes:</p>
        </div>

        <div class="flex justify-center mb-8">
            <div class="inline-flex flex-wrap justify-center p-1 bg-secondary/50 backdrop-blur-sm rounded-lg shadow-inner max-w-4xl gap-1">
                <button class="filter-btn px-4 py-2 rounded-md bg-accent text-white font-medium" data-filter="all">All Templates</button>
                <button class="filter-btn px-4 py-2 rounded-md text-light hover:bg-secondary/80 transition-colors" data-filter="professional">Professional</button>
                <button class="filter-btn px-4 py-2 rounded-md text-light hover:bg-secondary/80 transition-colors" data-filter="small-business">Small Business</button>
                <button class="filter-btn px-4 py-2 rounded-md text-light hover:bg-secondary/80 transition-colors" data-filter="portfolio">Portfolio</button>
                <button class="filter-btn px-4 py-2 rounded-md text-light hover:bg-secondary/80 transition-colors" data-filter="retail-ecommerce">Retail/E-Commerce</button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="template-gallery" role="region" aria-label="Template gallery">
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-primary/80 to-accent/50 flex items-center justify-center border-b-2 border-accent transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">AutoCare Center</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-accent/80 backdrop-blur-sm rounded-full">Automotive</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Automotive Repair</h3>
                    <p class="text-muted text-sm mb-4">Modern auto repair shop template with appointment booking system</p>
                </div>
                <a href="./Site Templates/1 Automotive Repair/automotive-repair.html" target="_blank"
                   aria-label="View Automotive Repair template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-orange-600/90 to-blue-600/50 flex items-center justify-center border-b-2 border-orange-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">FixIt Handyman</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-orange-500/80 backdrop-blur-sm rounded-full">Home Services</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Handyman Services</h3>
                    <p class="text-muted text-sm mb-4">Professional handyman template with service booking and contact form</p>
                </div>
                <a href="./Site Templates/2 Handyman/handyman-services.html" target="_blank"
                   aria-label="View Handyman Services template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-cyan-500/90 to-amber-400/50 flex items-center justify-center border-b-2 border-cyan-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">SparkleClean</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-cyan-500/80 backdrop-blur-sm rounded-full">Cleaning Services</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">House Cleaning</h3>
                    <p class="text-muted text-sm mb-4">Professional cleaning service template with quote request and service details</p>
                </div>
                <a href="./Site Templates/3 House Cleaning/house-cleaning.html" target="_blank"
                   aria-label="View House Cleaning template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-green-600/90 to-amber-400/50 flex items-center justify-center border-b-2 border-green-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">GreenThumb</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-green-500/80 backdrop-blur-sm rounded-full">Landscaping</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Landscaping Services</h3>
                    <p class="text-muted text-sm mb-4">Professional landscaping template with service details and free estimate request</p>
                </div>
                <a href="./Site Templates/4 Landscaping/landscaping-lawn-care.html" target="_blank"
                   aria-label="View Landscaping Services template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-900/90 to-blue-500/50 flex items-center justify-center border-b-2 border-blue-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Premier Real Estate</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-blue-500/80 backdrop-blur-sm rounded-full">Real Estate</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Real Estate Agent</h3>
                    <p class="text-muted text-sm mb-4">Professional real estate template with property listings and consultation booking</p>
                </div>
                <a href="./Site Templates/5 Real Estate/real-estate-agent.html" target="_blank"
                   aria-label="View Real Estate Agent template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-purple-400/90 to-green-400/50 flex items-center justify-center border-b-2 border-purple-300 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Dream Weddings</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-purple-400/80 backdrop-blur-sm rounded-full">Wedding Planning</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Wedding Planner</h3>
                    <p class="text-muted text-sm mb-4">Elegant wedding planning template with consultation booking and service details</p>
                </div>
                <a href="./Site Templates/6 Dream Weddings/Example.html" target="_blank"
                   aria-label="View Wedding Planner template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="portfolio">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-800/90 to-orange-500/50 flex items-center justify-center border-b-2 border-orange-400 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Alex Wanderlust</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-orange-500/80 backdrop-blur-sm rounded-full">Travel Photography</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Travel Photographer</h3>
                    <p class="text-muted text-sm mb-4">Modern travel photography blog with portfolio showcase and destination stories</p>
                </div>
                <a href="./Site Templates/Alex Wanderlust/index.html" target="_blank"
                   aria-label="View Travel Photographer template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-indigo-600/90 to-pink-500/50 flex items-center justify-center border-b-2 border-indigo-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Pixel Perfect</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-indigo-500/80 backdrop-blur-sm rounded-full">Digital Marketing</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Marketing Agency</h3>
                    <p class="text-muted text-sm mb-4">Modern digital marketing agency template with portfolio showcase and service details</p>
                </div>
                <a href="./Site Templates/Pixel Perfect/index.html"  target="_blank"
                   aria-label="View Marketing Agency template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="portfolio">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-indigo-600/90 to-sky-500/50 flex items-center justify-center border-b-2 border-indigo-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">CloudSync</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-indigo-500/80 backdrop-blur-sm rounded-full">SaaS Product</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">SaaS Landing Page</h3>
                    <p class="text-muted text-sm mb-4">Modern SaaS product landing page with pricing tables and feature showcase</p>
                </div>
                <a href="./Site Templates/CloudSync/CloudSync/index.html" target="_blank"
                   aria-label="View SaaS Landing Page template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="portfolio">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-indigo-500/90 to-pink-500/50 flex items-center justify-center border-b-2 border-indigo-400 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">ColorCraft</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-indigo-400/80 backdrop-blur-sm rounded-full">Design Studio</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Color Design Studio</h3>
                    <p class="text-muted text-sm mb-4">Professional color consultation service with portfolio showcase and color theory education</p>
                </div>
                <a href="./Site Templates/ColorCraft/index.html" target="_blank"
                   aria-label="View Color Design Studio template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-600/90 to-green-500/50 flex items-center justify-center border-b-2 border-blue-400 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Summit Trekkers</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-blue-500/80 backdrop-blur-sm rounded-full">Hiking Club</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Outdoor Adventure Club</h3>
                    <p class="text-muted text-sm mb-4">Modern hiking club website with adventure listings, event schedule, and membership registration</p>
                </div>
                <a href="./Site Templates/Summit Trekkers/starting/index.html" target="_blank"
                   aria-label="View Hiking Club template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>

            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="retail-ecommerce">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-rose-600/90 to-slate-800/90 flex items-center justify-center border-b-2 border-rose-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">Vinyl Vault</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-rose-500/80 backdrop-blur-sm rounded-full">Record Store</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Premium Record Store</h3>
                    <p class="text-muted text-sm mb-4">Stylish vinyl record shop website with featured releases, collection showcase, and event listings</p>
                </div>
                <a href="./Site Templates/Vinyl Vault/index.html" target="_blank"
                   aria-label="View Record Store template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="small-business">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-400/90 to-teal-400/90 flex items-center justify-center border-b-2 border-blue-300 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">OceanBreeze</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-blue-400/80 backdrop-blur-sm rounded-full">Beach Resort</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Luxury Beach Resort</h3>
                    <p class="text-muted text-sm mb-4">Elegant beachfront resort website with accommodation showcase, dining options, and spa services</p>
                </div>
                <a href="./Site Templates/OceanBreeze/index.html" target="_blank"
                   aria-label="View Beach Resort template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
           <!-- NEW ShopSmart Template Card START -->
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="retail-ecommerce">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-sky-600/90 to-lime-400/50 flex items-center justify-center border-b-2 border-sky-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">ShopSmart</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-sky-500/80 backdrop-blur-sm rounded-full">E-commerce</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">ShopSmart E-commerce</h3>
                    <p class="text-muted text-sm mb-4">Modern, versatile e-commerce template with product grid, cart, and more.</p>
                </div>
                <a href="./Site Templates/ShopSmart/index.html" target="_blank"
                   aria-label="View ShopSmart E-commerce template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <!-- NEW ShopSmart Template Card END -->

            <!-- NEW MedCare Template Card START -->
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-600/90 to-green-500/50 flex items-center justify-center border-b-2 border-blue-500 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">MedCare</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-blue-500/80 backdrop-blur-sm rounded-full">Healthcare</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Medical Practice</h3>
                    <p class="text-muted text-sm mb-4">Professional healthcare template with appointment booking, staff profiles, and service details.</p>
                </div>
                <a href="./Site Templates/MedCare/medcare.html" target="_blank"
                   aria-label="View Medical Practice template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <!-- NEW MedCare Template Card END -->

            <!-- NEW EduConnect Template Card START -->
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-500/90 to-purple-500/50 flex items-center justify-center border-b-2 border-blue-400 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">EduConnect</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-blue-500/80 backdrop-blur-sm rounded-full">Education</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Online Learning Platform</h3>
                    <p class="text-muted text-sm mb-4">Modern educational website template with course listings, faculty profiles, and student testimonials</p>
                </div>
                <a href="./Site Templates/EduConnect/index.html" target="_blank"
                   aria-label="View Education Platform template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <!-- NEW EduConnect Template Card END -->

            <!-- NEW LexCounsel Law Firm Template Card START -->
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-slate-700/90 to-blue-700/50 flex items-center justify-center border-b-2 border-slate-600 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">LexCounsel</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-slate-600/80 backdrop-blur-sm rounded-full">Law Firm</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Professional Law Practice</h3>
                    <p class="text-muted text-sm mb-4">Sophisticated law firm template with practice areas, attorney profiles, and client testimonials</p>
                </div>
                <a href="./Site Templates/LexCounsel/index.html" target="_blank"
                   aria-label="View Law Firm template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <!-- NEW LexCounsel Law Firm Template Card END -->

            <!-- NEW FinConsult Accounting Template Card START -->
            <div class="template-card bg-secondary rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border border-white/5 group" data-category="professional">
                <div class="relative overflow-hidden">
                    <div class="w-full h-56 bg-gradient-to-r from-blue-900/90 to-green-600/50 flex items-center justify-center border-b-2 border-green-600 transition-transform duration-700 group-hover:scale-105">
                        <span class="text-light text-xl font-bold">FinConsult</span>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                        <span class="text-light text-sm font-medium px-3 py-1 bg-green-600/80 backdrop-blur-sm rounded-full">Accounting Firm</span>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-light mb-2">Financial Consulting</h3>
                    <p class="text-muted text-sm mb-4">Professional accounting and consulting firm template with service showcase and financial expertise</p>
                </div>
                <a href="./Site Templates/FinConsult/index.html" target="_blank"
                   aria-label="View Financial Consulting template"
                   class="block">
                    <button class="w-full py-4 bg-accent text-light font-bold relative overflow-hidden group-hover:shadow-inner">
                        <span class="relative z-10">View Template</span>
                        <span class="absolute top-0 left-0 w-0 h-full bg-highlight transition-all duration-500 group-hover:w-full -z-1"></span>
                    </button>
                </a>
            </div>
            <!-- NEW FinConsult Accounting Template Card END -->
        </div>
    </main>
    <footer class="bg-secondary mt-16 py-12 relative overflow-hidden">
        <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent opacity-50"></div>

        <!-- Footer Wave SVG -->
        <div class="absolute top-0 left-0 w-full overflow-hidden leading-none transform rotate-180">
            <svg class="relative block w-full h-12" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="fill-primary"></path>
            </svg>
        </div>

        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-3 gap-12 items-start mb-8 px-2">
                <div>
                    <h3 style="margin: 1rem;" class="text-xl font-bold mb-6">
                        <span class="text-light relative inline-block">About<br>Us</span>

                    </h3>
                    <p class="text-muted mb-4 leading-relaxed mt-2">We create websites that don't just look good—they work hard for your business. Our focus is on design that converts visitors into customers.</p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="w-10 h-10 rounded-full bg-accent/20 flex items-center justify-center text-accent hover:bg-accent hover:text-white transition-all duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-accent/20 flex items-center justify-center text-accent hover:bg-accent hover:text-white transition-all duration-300">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-accent/20 flex items-center justify-center text-accent hover:bg-accent hover:text-white transition-all duration-300">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div class="text-center">
                    <img id="footer-logo" src="./images/Logo.png" alt="BytesByBlinken Logo" class="w-24 mx-auto mb-4">
                    <p class="text-muted">Websites that work as hard as you do</p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4">
                        <span class="text-light relative inline-block">Contact<br>Us</span>
                        <span Class="absolute -bottom-1 left-0 w-full h-0.5 bg-accent"></span>
                    </h3>
                    <address class="not-italic text-muted">
                        <p style="margin: 1rem;" class="mb-2 flex items-center">
                            <i class="fas fa-envelope mr-3 text-accent"></i>
                            <a href="mailto:<EMAIL>" class="hover:text-accent transition-colors"><EMAIL></a>
                        </p>
                        <p style="margin: 1rem;" class="mb-2 flex items-center">
                            <i class="fas fa-clock mr-3 text-accent"></i>
                            Response within 48 hours
                        </p>
                    </address>
                </div>
            </div>

            <div class="border-t border-white/10 pt-6 text-center">
                <p class="text-muted text-sm">&copy; 2025 BytesByBlinken Media. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all filter buttons and template cards
            const filterButtons = document.querySelectorAll('.filter-btn');
            const templateCards = document.querySelectorAll('.template-card');

            // Add click event listeners to filter buttons
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => {
                        btn.classList.remove('bg-accent', 'text-white');
                        btn.classList.add('text-light', 'hover:bg-secondary/80');
                    });

                    // Add active class to clicked button
                    this.classList.remove('text-light', 'hover:bg-secondary/80');
                    this.classList.add('bg-accent', 'text-white');

                    // Get filter value
                    const filterValue = this.getAttribute('data-filter');

                    // Filter template cards
                    templateCards.forEach(card => {
                        // Show all cards if 'all' is selected
                        if (filterValue === 'all') {
                            card.style.display = 'block';
                            // Add animation
                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'scale(1)';
                            }, 50);
                        } else {
                            // Check if card has the selected category
                            if (card.getAttribute('data-category') === filterValue) {
                                card.style.display = 'block';
                                // Add animation
                                setTimeout(() => {
                                    card.style.opacity = '1';
                                    card.style.transform = 'scale(1)';
                                }, 50);
                            } else {
                                // Hide cards that don't match
                                card.style.opacity = '0';
                                card.style.transform = 'scale(0.8)';
                                setTimeout(() => {
                                    card.style.display = 'none';
                                }, 300);
                            }
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>