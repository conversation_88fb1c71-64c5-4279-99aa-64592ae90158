/* EduConnect - Modern Education Platform Styles */
:root {
    --primary-color: #007bff;    --primary-dark: #0056b3;
    --secondary-color: #343a40;    --accent-color: #28a745;
    --text-color: #343a40;    --text-light: #6c757d;
    --white: #ffffff;    --light-bg: #f8f9fa;
    --border-radius: 8px;    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;}
/* Base Styles */
* {    
    margin: 0;
    padding: 0;   
    box-sizing: border-box;
}

body {    
    font-family: 'Plus Jakarta Sans', sans-serif;
    line-height: 1.6;    color: var(--text-color);
    background-color: var(--light-bg);
}

.container {
    max-width: 1200px;    margin: 0 auto;
    padding: 0 20px;}
/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Navigation Styles */
.main-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 0;
}

.main-nav a:hover {
    color: var(--primary-color);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-arrow {
    font-size: 0.8rem;
    margin-left: 0.25rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.75rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    display: block;
    margin: 0;
}

.dropdown-menu a {
    padding: 0.5rem 1.5rem;
    display: block;
    white-space: nowrap;
}

.dropdown-menu a:hover {
    background-color: var(--light-bg);
}

/* Header Actions */
.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.login-btn,
.signup-btn {
    display: inline-block;
    padding: 1rem 2rem;
    min-width: 140px; /* Added fixed minimum width */
    text-align: center; /* Ensure text is centered */
    background-color: var(--accent-color);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
}

.login-btn {
    background-color: transparent;
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
}

.login-btn:hover,
.signup-btn:hover {
    background-color: #218838;
    transform: translateY(-2px);
}

.login-btn:hover {
    color: var(--white);
}

/* Responsive adjustments for header actions */
@media (max-width: 992px) {
    .header-actions {
        margin-top: 1rem;
        flex-direction: column;
        width: 100%;
        gap: 1rem;
    }

    .login-btn,
    .signup-btn {
        width: 100%;
        text-align: center;
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
}

/* Responsive Header */
@media (max-width: 992px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-nav {
        position: fixed;
        top: 72px;
        left: 0;
        width: 100%;
        background-color: var(--white);
        padding: 1rem;
        box-shadow: var(--box-shadow);
        flex-direction: column;
        gap: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }

    .main-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .main-nav ul {
        flex-direction: column;
        width: 100%;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        padding-left: 1rem;
        opacity: 1;
        visibility: visible;
        transform: none;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .login-btn,
    .signup-btn {
        width: 100%;
        text-align: center;
    }
}

/* Add margin to main content to account for fixed header */
main {
    margin-top: 72px;
}
/* Hero Section */
#hero {    padding: 8rem 0 4rem;
}
#hero .container {    background-size: cover;
    background-position: center;    border-radius: var(--border-radius);
    padding: 4rem;    color: var(--white);
    text-align: center;    position: relative;
}
#hero .container::before {    content: '';
    position: absolute;    top: 0;
    left: 0;    right: 0;
    bottom: 0;    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius);}
#hero h1 {
    font-size: 3rem;    font-weight: 800;
    margin-bottom: 1.5rem;    position: relative;
}
#hero p {    font-size: 1.2rem;
    max-width: 600px;    margin: 0 auto 2rem;
    position: relative;}
.cta-button {
    display: inline-block;    padding: 1rem 2rem;
    background-color: var(--accent-color);    color: var(--white);
    text-decoration: none;    border-radius: var(--border-radius);
    font-weight: 600;    transition: var(--transition);
    position: relative;}
.cta-button:hover {
    background-color: #218838;    transform: translateY(-2px);
}
/* Course Section */#courses {
    padding: 4rem 0;}
.course-grid {
    display: grid;    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;    margin-top: 2rem;
}
.course-card {    background: var(--white);
    border-radius: var(--border-radius);    overflow: hidden;
    box-shadow: var(--box-shadow);    transition: var(--transition);
}
.course-card:hover {    transform: translateY(-5px);
}
.course-card img {    width: 100%;
    height: 200px;    object-fit: cover;
}
.course-card h3 {    padding: 1.5rem 1.5rem 0.5rem;
    font-size: 1.25rem;    font-weight: 700;
}
.course-card p {    padding: 0 1.5rem 1.5rem;
    color: var(--text-light);}
.duration {
    display: inline-block;    padding: 0.25rem 1rem;
    background-color: var(--light-bg);    border-radius: var(--border-radius);
    margin: 0 1.5rem 1.5rem;    font-size: 0.875rem;
    color: var(--text-light);}
/* Responsive Design */
@media (max-width: 768px) {    .header-container {
        flex-direction: column;        padding: 1rem;
    }
    .main-nav {        margin-top: 1rem;
    }
    .main-nav ul {        flex-direction: column;
        text-align: center;        gap: 1rem;
    }
    #hero h1 {        font-size: 2rem;
    }
    .course-grid {        grid-template-columns: 1fr;
    }
}

/* Footer Styles */
footer {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 4rem 0 0;
    margin-top: 4rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-section h3 {
    color: var(--white);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -0.5rem;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--white);
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
}

.contact-info li i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
    margin-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.legal-links {
    display: flex;
    gap: 1.5rem;
}

.legal-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.legal-links a:hover {
    color: var(--white);
}

/* Responsive Footer */
@media (max-width: 992px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .legal-links {
        justify-content: center;
    }
}

/* Contact Form Section */
#contact {
    padding: 4rem 0;
    background-color: var(--white);
}

#contact h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2.5rem;
    color: var(--text-color);
}

.contact-form {
    max-width: 700px;
    margin: 0 auto;
    padding: 2.5rem;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-family: 'Plus Jakarta Sans', sans-serif;
    font-size: 1rem;
    color: var(--text-color);
    background-color: var(--light-bg);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    background-color: var(--white);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23666' viewBox='0 0 16 16'%3E%3Cpath d='M8 11.5l-5-5h10l-5 5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

.contact-form button {
    display: block;
    width: 100%;
    padding: 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.contact-form button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.contact-form button:active {
    transform: translateY(0);
}

/* Form Validation Styles */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: #dc3545;
}

.form-group input:invalid:focus,
.form-group select:invalid:focus,
.form-group textarea:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .contact-form {
        padding: 1.5rem;
    }

    #contact h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }
}

@media (max-width: 480px) {
    .contact-form {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    #contact h2 {
        font-size: 1.75rem;
        margin-bottom: 1.5rem;
    }
}

/* Section Headers */
section {
    padding: 5rem 0;
}

section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-color);
    margin-bottom: 3rem;
    position: relative;
}

section h2::after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

/* Faculty Section */
.faculty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.faculty-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.faculty-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.faculty-card img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    border-bottom: 3px solid var(--primary-color);
}

.faculty-card h3 {
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 1.5rem 1.5rem 0.5rem;
}

.faculty-card p {
    color: var(--text-light);
    padding: 0 1.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.faculty-card p:last-child {
    margin-bottom: 1.5rem;
    font-style: italic;
}

/* Testimonials Section */
#testimonials {
    background-color: var(--light-bg);
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.1;
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-card img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.5rem;
    border: 3px solid var(--primary-color);
}

.testimonial-card blockquote {
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
    position: relative;
    z-index: 1;
}

.testimonial-card cite {
    color: var(--primary-color);
    font-style: normal;
    font-weight: 600;
    display: block;
    margin-top: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    section h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .faculty-grid,
    .testimonial-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .faculty-card img {
        height: 240px;
    }

    .testimonial-card {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    section h2 {
        font-size: 1.75rem;
    }

    .faculty-card,
    .testimonial-card {
        text-align: center;
    }
}
