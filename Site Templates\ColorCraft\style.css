/* ColorCraft - Professional Color Design Studio Styles */
:root {
  --primary-color: #6366f1; /* Indigo */
  --primary-dark: #4f46e5;
  --primary-light: #a5b4fc;
  --secondary-color: #ec4899; /* Pink */
  --secondary-dark: #db2777;
  --accent-color: #f59e0b; /* Amber */
  --text-color: #1e293b;
  --text-light: #64748b;
  --white: #ffffff;
  --light-bg: #f8fafc;
  --gray-bg: #f1f5f9;
  --dark-bg: #0f172a;
  --border-radius: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Raleway', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  background-color: var(--white);
  font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-color);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--primary-color);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-header p {
  max-width: 600px;
  margin: 15px auto 0;
  color: var(--text-light);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: 'Raleway', sans-serif;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--text-light);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-3px);
}

.btn-block {
  display: block;
  width: 100%;
}

/* Header */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--text-color);
}

.logo-icon {
  color: var(--primary-color);
  margin-right: 8px;
  font-size: 1.8rem;
}

.main-nav ul {
  display: flex;
  list-style: none;
}

.main-nav li {
  margin: 0 15px;
}

.main-nav a {
  color: var(--text-color);
  font-weight: 500;
  position: relative;
  padding: 5px 0;
}

.main-nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav a:hover::after {
  width: 100%;
}

.main-nav a.btn {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 8px 16px;
  border-radius: var(--border-radius);
}

.main-nav a.btn:hover {
  background-color: var(--primary-dark);
}

.main-nav a.btn::after {
  display: none;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 24px;
  position: relative;
  z-index: 1001;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-color);
  position: absolute;
  left: 0;
  transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
  top: 0;
}

.mobile-menu-toggle span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
  bottom: 0;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 11px;
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 11px;
}

/* Hero Section */
.hero-section {
  padding: 120px 0 80px;
  position: relative;
  background-color: var(--light-bg);
  overflow: hidden;
}

.hero-section .container {
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 30px;
  max-width: 600px;
}

.hero-cta {
  display: flex;
  gap: 15px;
}

.hero-image {
  flex: 1;
  position: relative;
}

.hero-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.color-bar {
  display: flex;
  width: 100%;
  height: 8px;
}

.color-segment {
  flex: 1;
}

/* Services Section */
.services-section {
  padding: 100px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.service-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  width: 70px;
  height: 70px;
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 28px;
  transition: var(--transition);
}

.service-card:hover .service-icon {
  background-color: var(--primary-color);
  color: var(--white);
}

.service-card h3 {
  margin-bottom: 15px;
}

.service-card p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.service-link {
  color: var(--primary-color);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.service-link i {
  margin-left: 5px;
  transition: var(--transition);
}

.service-link:hover i {
  transform: translateX(5px);
}

/* Color Theory Section */
.color-theory-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.color-theory-content {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 60px;
}

.color-wheel {
  flex: 0 0 40%;
}

.color-wheel img {
  border-radius: 50%;
  box-shadow: var(--box-shadow);
}

.color-principles {
  flex: 1;
}

.principle {
  margin-bottom: 30px;
}

.principle:last-child {
  margin-bottom: 0;
}

.principle h3 {
  margin-bottom: 10px;
  color: var(--primary-color);
}

.principle p {
  margin-bottom: 0;
}

.color-attributes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.attribute {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
}

.attribute h3 {
  margin-bottom: 10px;
  color: var(--primary-color);
}

.attribute p {
  margin-bottom: 20px;
  color: var(--text-light);
}

.attribute-visual {
  display: flex;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.attribute-visual div {
  flex: 1;
}

/* Portfolio Section */
.portfolio-section {
  padding: 100px 0;
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  background: none;
  border: none;
  padding: 8px 20px;
  margin: 0 5px 10px;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  border-radius: 20px;
}

.filter-btn.active,
.filter-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.portfolio-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  height: 300px;
}

.portfolio-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: flex-end;
  padding: 30px;
  opacity: 0;
  transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-item:hover img {
  transform: scale(1.1);
}

.portfolio-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 0.8rem;
  padding: 5px 10px;
  border-radius: 3px;
  margin-bottom: 10px;
}

.portfolio-info h3 {
  color: var(--white);
  margin-bottom: 15px;
}

.portfolio-link {
  color: var(--white);
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: underline;
}

/* About Section */
.about-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.about-text {
  flex: 1;
}

.about-text p {
  margin-bottom: 25px;
}

.about-stats {
  display: flex;
  margin-top: 40px;
}

.stat {
  flex: 1;
  text-align: center;
  padding: 0 15px;
  border-right: 1px solid #e2e8f0;
}

.stat:last-child {
  border-right: none;
}

.stat-number {
  display: block;
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Testimonials Section */
.testimonials-section {
  padding: 100px 0;
}

.testimonials-slider {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  margin-bottom: 30px;
}

.testimonial:last-child {
  margin-bottom: 0;
}

.testimonial-content {
  position: relative;
  padding: 20px 0;
}

.testimonial-content::before,
.testimonial-content::after {
  content: '"';
  font-family: 'Playfair Display', serif;
  font-size: 4rem;
  color: var(--primary-light);
  position: absolute;
  line-height: 1;
}

.testimonial-content::before {
  top: -20px;
  left: -10px;
}

.testimonial-content::after {
  bottom: -60px;
  right: -10px;
}

.testimonial-content p {
  font-style: italic;
  margin-bottom: 0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
}

.author-info h4 {
  margin-bottom: 5px;
}

.author-info p {
  color: var(--text-light);
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.contact-info h2 {
  margin-bottom: 20px;
}

.contact-info > p {
  margin-bottom: 40px;
}

.contact-details {
  margin-bottom: 40px;
}

.contact-item {
  display: flex;
  margin-bottom: 25px;
}

.contact-item i {
  flex: 0 0 40px;
  height: 40px;
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.contact-item h3 {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.contact-item p {
  color: var(--text-light);
  margin-bottom: 0;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--white);
  color: var(--primary-color);
  border-radius: 50%;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.social-links a:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-3px);
}

.contact-form {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: 'Raleway', sans-serif;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Footer */
.site-footer {
  background-color: var(--dark-bg);
  color: var(--white);
  padding: 80px 0 0;
  position: relative;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-logo .logo {
  color: var(--white);
  margin-bottom: 15px;
}

.footer-logo p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}

.footer-links h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-newsletter h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-newsletter h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-newsletter p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  font-family: inherit;
  font-size: 0.95rem;
}

.newsletter-form button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0 20px;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background-color: var(--primary-dark);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal a:hover {
  color: var(--primary-color);
}

.site-footer .color-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  h2 {
    font-size: 2.2rem;
  }
  
  .hero-section .container,
  .color-theory-content,
  .about-content {
    gap: 40px;
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .main-nav {
    display: none;
  }
  
  .main-nav.active {
    display: block;
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .main-nav.active ul {
    flex-direction: column;
  }
  
  .main-nav.active li {
    margin: 10px 0;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .hero-section .container {
    flex-direction: column;
    text-align: center;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .color-theory-content {
    flex-direction: column;
    text-align: center;
  }
  
  .color-wheel {
    margin-bottom: 30px;
  }
  
  .about-content {
    flex-direction: column;
    text-align: center;
  }
  
  .about-image {
    order: -1;
    margin-bottom: 30px;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .footer-links h3::after,
  .footer-newsletter h3::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .newsletter-form {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
  }
  
  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .hero-section {
    padding: 100px 0 60px;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 15px;
  }
  
  .services-section,
  .color-theory-section,
  .portfolio-section,
  .about-section,
  .testimonials-section,
  .contact-section {
    padding: 60px 0;
  }
  
  .about-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .stat {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 20px;
  }
  
  .stat:last-child {
    border-bottom: none;
  }
  
  .contact-form {
    padding: 30px 20px;
  }
}
