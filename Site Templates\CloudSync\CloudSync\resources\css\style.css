/* CloudSync - Modern SaaS Landing Page Styles */
:root {
  --primary-color: #4f46e5; /* Indigo */
  --primary-dark: #4338ca;
  --primary-light: #818cf8;
  --secondary-color: #0ea5e9; /* Sky blue */
  --secondary-dark: #0284c7;
  --accent-color: #f97316; /* Orange */
  --text-color: #1e293b;
  --text-light: #64748b;
  --white: #ffffff;
  --light-bg: #f8fafc;
  --gray-bg: #f1f5f9;
  --dark-bg: #0f172a;
  --border-radius: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  background-color: var(--white);
  font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-color);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--primary-color);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-header p {
  max-width: 600px;
  margin: 15px auto 0;
  color: var(--text-light);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: 'Inter', sans-serif;
}

.btn-large {
  padding: 14px 32px;
  font-size: 1.1rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
  border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--text-light);
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-3px);
}

.btn-block {
  display: block;
  width: 100%;
}

/* Header */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--text-color);
}

.logo i {
  color: var(--primary-color);
  margin-right: 8px;
  font-size: 1.8rem;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
}

.desktop-nav li {
  margin: 0 15px;
}

.desktop-nav a {
  color: var(--text-color);
  font-weight: 500;
  position: relative;
  padding: 5px 0;
}

.desktop-nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.desktop-nav a:hover::after {
  width: 100%;
}

.header-cta {
  display: flex;
  gap: 10px;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 24px;
  position: relative;
  z-index: 1001;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-color);
  position: absolute;
  left: 0;
  transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
  top: 0;
}

.mobile-menu-toggle span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
  bottom: 0;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 11px;
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 11px;
}

.mobile-nav {
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;
  background-color: var(--white);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: 999;
}

.mobile-nav.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav ul {
  list-style: none;
}

.mobile-nav li {
  margin-bottom: 15px;
}

.mobile-nav a {
  color: var(--text-color);
  font-weight: 500;
  display: block;
  padding: 8px 0;
}

.mobile-nav .btn {
  margin-top: 10px;
  display: inline-block;
}

/* Main Content */
.main-content {
  padding-top: 70px; /* To offset for fixed header */
}

/* Hero Section */
.hero-section {
  padding: 80px 0;
  background-color: var(--light-bg);
  overflow: hidden;
}

.hero-section .container {
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 30px;
}

.hero-cta {
  display: flex;
  gap: 15px;
  margin-bottom: 40px;
}

.hero-cta .btn-outline i {
  margin-left: 8px;
}

.hero-stats {
  display: flex;
  gap: 30px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 1.8rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-light);
  font-size: 0.9rem;
}

.hero-image {
  flex: 1;
  position: relative;
}

.hero-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Features Section */
.features-section {
  padding: 100px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon {
  width: 70px;
  height: 70px;
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 28px;
  transition: var(--transition);
}

.feature-card:hover .feature-icon {
  background-color: var(--primary-color);
  color: var(--white);
}

.feature-card h3 {
  margin-bottom: 15px;
}

.feature-card p {
  color: var(--text-light);
  margin-bottom: 0;
}

/* How It Works Section */
.how-it-works-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.steps {
  max-width: 800px;
  margin: 0 auto 60px;
}

.step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
}

.step:last-child {
  margin-bottom: 0;
}

.step-number {
  flex: 0 0 50px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 1.2rem;
  margin-right: 20px;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  margin-bottom: 10px;
}

.step-content p {
  color: var(--text-light);
  margin-bottom: 0;
}

.how-it-works-image {
  max-width: 800px;
  margin: 0 auto;
}

.how-it-works-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Pricing Section */
.pricing-section {
  padding: 100px 0;
}

.pricing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.pricing-toggle span {
  color: var(--text-light);
  font-weight: 500;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
  margin: 0 15px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: var(--transition);
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: var(--white);
  transition: var(--transition);
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(30px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.save-badge {
  display: inline-block;
  background-color: var(--accent-color);
  color: var(--white);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 5px;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.pricing-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-10px);
}

.pricing-card.popular {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-10px);
}

.popular-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 0.8rem;
  font-weight: 600;
  padding: 5px 10px;
  border-radius: 20px;
}

.pricing-header {
  padding: 30px;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.pricing-header h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.price {
  margin-bottom: 15px;
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  vertical-align: top;
  position: relative;
  top: 0.3rem;
}

.amount {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
}

.amount.annual {
  display: none;
}

.pricing-card.annual .amount.monthly {
  display: none;
}

.pricing-card.annual .amount.annual {
  display: inline;
}

.period {
  font-size: 1rem;
  color: var(--text-light);
}

.pricing-header p {
  color: var(--text-light);
  margin-bottom: 0;
}

.pricing-features {
  padding: 30px;
}

.pricing-features ul {
  list-style: none;
}

.pricing-features li {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.pricing-features li i {
  margin-right: 10px;
  color: var(--primary-color);
}

.pricing-features li.disabled {
  color: var(--text-light);
  opacity: 0.7;
}

.pricing-features li.disabled i {
  color: #cbd5e1;
}

.pricing-cta {
  padding: 0 30px 30px;
}

.enterprise-cta {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.enterprise-content h3 {
  margin-bottom: 10px;
}

.enterprise-content p {
  color: var(--text-light);
  margin-bottom: 0;
  max-width: 600px;
}

/* Testimonials Section */
.testimonials-section {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-10px);
}

.testimonial-rating {
  margin-bottom: 20px;
  color: #f59e0b; /* Amber */
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 20px;
  color: var(--text-color);
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
}

.author-info h4 {
  margin-bottom: 5px;
}

.author-info p {
  color: var(--text-light);
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* FAQ Section */
.faq-section {
  padding: 100px 0;
}

.faq-grid {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.faq-question {
  padding: 20px;
  background-color: var(--white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-question h3 {
  margin-bottom: 0;
  font-size: 1.2rem;
}

.faq-toggle {
  color: var(--primary-color);
}

.faq-answer {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: var(--transition);
}

.faq-item.active .faq-answer {
  padding: 0 20px 20px;
  max-height: 1000px;
}

/* CTA Section */
.cta-section {
  padding: 100px 0;
  background-color: var(--primary-color);
  color: var(--white);
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.cta-content h2 {
  color: var(--white);
  margin-bottom: 20px;
}

.cta-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-section .btn-primary {
  background-color: var(--white);
  color: var(--primary-color);
  border-color: var(--white);
}

.cta-section .btn-primary:hover {
  background-color: transparent;
  color: var(--white);
}

.cta-section .btn-outline {
  color: var(--white);
  border-color: rgba(255, 255, 255, 0.5);
}

.cta-section .btn-outline:hover {
  border-color: var(--white);
  background-color: var(--white);
  color: var(--primary-color);
}

/* Footer */
.site-footer {
  background-color: var(--dark-bg);
  color: var(--white);
  padding: 80px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr repeat(3, 1fr) 2fr;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-logo .logo {
  color: var(--white);
  margin-bottom: 15px;
}

.footer-logo p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-radius: 50%;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-newsletter h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-newsletter h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-newsletter p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  font-family: inherit;
  font-size: 0.95rem;
}

.newsletter-form button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0 20px;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background-color: var(--primary-dark);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  h2 {
    font-size: 2.2rem;
  }
  
  .hero-section .container {
    gap: 40px;
  }
  
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-logo {
    grid-column: span 3;
  }
  
  .footer-newsletter {
    grid-column: span 3;
  }
}

@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .desktop-nav, .header-cta {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .hero-section .container {
    flex-direction: column;
  }
  
  .hero-content {
    text-align: center;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .enterprise-cta {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-logo, .footer-newsletter {
    grid-column: span 1;
  }
  
  .footer-links h3::after,
  .footer-newsletter h3::after {
    left: 0;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .hero-section {
    padding: 60px 0;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 15px;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .features-section,
  .how-it-works-section,
  .pricing-section,
  .testimonials-section,
  .faq-section,
  .cta-section {
    padding: 60px 0;
  }
  
  .pricing-card.popular {
    transform: scale(1);
  }
  
  .pricing-card.popular:hover {
    transform: translateY(-10px);
  }
}
