/* 
FinConsult - Professional Accounting & Consulting Firm Template
Designed with a clean, modern, and sophisticated aesthetic
*/

/* --- Base Styles --- */
:root {
    /* Color Palette */
    --primary-color: #00294d;  /* Deep blue - primary brand color */
    --secondary-color: #00843d; /* Green accent */
    --dark-color: #19202d;     /* Near black for text */
    --light-color: #f8f9fa;    /* Off-white for backgrounds */
    --grey-color: #6c757d;     /* Medium grey for secondary text */
    --light-grey: #e9ecef;     /* Light grey for backgrounds */
    --border-color: #dee2e6;   /* Border color */
    
    /* Typography */
    --heading-font: 'Montserrat', sans-serif;
    --body-font: 'Open Sans', sans-serif;
    
    /* Spacing */
    --section-padding: 6rem 0;
    --container-padding: 0 2rem;
    
    /* Shadows */
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius: 8px;
}

/* Reset & Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    color: var(--dark-color);
    line-height: 1.6;
    background-color: var(--light-color);
    overflow-x: hidden;
    padding-top: 80px;
}

img {
    max-width: 100%;
    height: auto;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-normal);
}

a:hover {
    color: var(--secondary-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1.5rem;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.site-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

/* --- Utility Classes --- */
.img-fluid {
    width: 100%;
    height: auto;
    display: block;
}

.section-padding {
    padding: var(--section-padding);
}

.bg-light {
    background-color: var(--light-grey);
}

.lead-text {
    font-size: 1.25rem;
    line-height: 1.7;
    font-weight: 300;
    margin-bottom: 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.section-divider {
    height: 3px;
    width: 60px;
    background-color: var(--secondary-color);
    margin: 0 auto 1.5rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--grey-color);
    max-width: 600px;
    margin: 0 auto;
}

/* --- Button Styles --- */
.btn {
    display: inline-block;
    font-weight: 500;
    font-family: var(--body-font);
    text-align: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #001f3a; /* Darker shade of primary */
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--card-shadow);
}

.btn-secondary {
    background-color: transparent;
    color: var(--light-color);
    border: 2px solid var(--light-color);
}

.btn-secondary:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* --- Header Styles --- */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-normal);
    height: 80px;
}

.site-header .container {
    height: 100%;
}

.header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    text-decoration: none;
}

.logo-text {
    font-family: var(--heading-font);
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    transition: color var(--transition-normal);
    letter-spacing: -0.5px;
}

.logo-text .accent {
    color: var(--secondary-color);
}

.main-nav {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-list li {
    list-style: none;
}

.nav-link {
    font-weight: 500;
    font-size: 1rem;
    color: var(--dark-color);
    padding: 0.75rem 0;
    position: relative;
    transition: color var(--transition-normal);
    text-decoration: none;
}

.nav-list li a {
    position: relative;
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color var(--transition-normal);
}

.nav-list li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: width var(--transition-normal);
}

.nav-list li a:hover {
    color: var(--primary-color);
}

.nav-list li a:hover::after,
.nav-list li a.active::after {
    width: 100%;
}

.nav-list li a.active {
    color: var(--primary-color);
    font-weight: 600;
}

.header-cta {
    margin-left: 2.5rem;
}

.header-cta .btn {
    padding: 0.6rem 1.25rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
}

.hamburger {
    display: block;
    position: relative;
    width: 24px;
    height: 2px;
    background-color: var(--primary-color);
    transition: all 0.3s;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 2px;
    background-color: var(--primary-color);
    transition: all 0.3s;
}

.hamburger::before {
    transform: translateY(-8px);
}

.hamburger::after {
    transform: translateY(8px);
}

/* Header on scroll style */
.site-header.scrolled {
    height: 70px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.98);
}

.site-header.scrolled .logo-text {
    font-size: 1.8rem;
}

/* --- Hero Section --- */
.hero-section {
    height: 80vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?q=80&w=2070');
    background-size: cover;
    background-position: center;
    color: white;
    position: relative;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    color: white;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* --- About Section --- */
.about-content {
    display: grid;
    grid-template-columns: 6fr 4fr;
    gap: 4rem;
    align-items: center;
}

.about-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    text-align: center;
}

.stat {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    font-family: var(--heading-font);
    margin-bottom: 0.5rem;
}

.stat-text {
    font-size: 0.9rem;
    color: var(--grey-color);
}

.about-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    content: url('https://images.unsplash.com/photo-1664575197229-3bbebc281874?q=80&w=2070');
}

/* --- Services Section --- */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.service-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    height: 70px;
    background-color: rgba(0, 41, 77, 0.1);
    border-radius: 50%;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.service-title {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.service-description {
    font-size: 0.95rem;
    color: var(--grey-color);
    margin-bottom: 1.5rem;
}

.service-link {
    font-weight: 600;
    color: var(--secondary-color);
    position: relative;
}

.service-link::after {
    content: '→';
    margin-left: 0.5rem;
    transition: transform var(--transition-normal);
}

.service-link:hover::after {
    transform: translateX(5px);
}

/* --- Team Section --- */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
}

.team-card {
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.team-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
}

.team-card:hover .team-image img {
    transform: scale(1.05);
}

.team-info {
    padding: 1.5rem;
    text-align: center;
}

.team-name {
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
}

.team-title {
    color: var(--grey-color);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.team-specialty {
    color: var(--secondary-color);
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-contact {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.team-contact-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: rgba(0, 41, 77, 0.1);
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.team-contact-link:hover {
    background-color: var(--primary-color);
    color: white;
}

/* --- Testimonials Section --- */
.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto 2rem;
}

.testimonial-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 3rem 2rem 2rem;
    box-shadow: var(--card-shadow);
    position: relative;
}

.quote-icon {
    position: absolute;
    top: 1.5rem;
    left: 2rem;
    color: var(--secondary-color);
    font-size: 1.5rem;
    opacity: 0.2;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1.5rem;
}

.testimonial-author {
    text-align: right;
}

.author-name {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
}

.author-title {
    color: var(--grey-color);
    font-size: 0.85rem;
}

.testimonial-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.testimonial-prev,
.testimonial-next {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: var(--grey-color);
    transition: color var(--transition-normal);
}

.testimonial-prev:hover,
.testimonial-next:hover {
    color: var(--primary-color);
}

.testimonial-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    transition: background-color var(--transition-normal);
    cursor: pointer;
}

.dot.active {
    background-color: var(--secondary-color);
}

/* --- Contact Section --- */
.contact-section {
    background-color: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.contact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background-color: rgba(0, 41, 77, 0.1);
    border-radius: 50%;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.3rem;
    color: var(--primary-color);
}

.contact-details h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-details p {
    color: var(--grey-color);
    margin-bottom: 0.3rem;
}

.contact-info .social-links {
    margin-top: 2rem;
    display: flex;
    flex-direction: row;
    gap: 1rem;
}

.contact-info .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    transition: all var(--transition-normal);
    margin-right: 0;
}

.contact-info .social-links a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.contact-form-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2.5rem;
    box-shadow: var(--card-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-family: var(--body-font);
    transition: border-color var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* --- Footer Styles --- */
.site-footer {
    margin-top: 2rem;
    background-color: var(--primary-color);
    color: #ffffff;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    padding: 4rem 0;
}

.footer-info, .footer-links, .footer-contact {
    color: #ffffff;
}

.footer-info p, .footer-contact p {
    color: #ffffff;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.footer-logo {
    font-family: var(--heading-font);
    font-size: 1.6rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    display: inline-block;
}

.footer-social {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transition: all var(--transition-normal);
    margin-right: 0;
}

.footer-social a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-links h3, .footer-contact h3 {
    color: #ffffff;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.footer-links h3::after, .footer-contact h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-links ul {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.footer-links a {
    color: #ffffff;
    transition: all var(--transition-normal);
    font-weight: 500;
    opacity: 0.9;
}

.footer-links a:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.footer-contact address {
    font-style: normal;
}

.footer-contact address p {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: flex-start;
}

.footer-contact address p i {
    margin-right: 10px;
    color: var(--secondary-color);
}

.footer-bottom {
    background-color: #001f3a; /* Darker primary */
    padding: 1.5rem 0;
    color: #ffffff;
}

.footer-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.legal-links {
    display: flex;
    gap: 2rem;
}

.legal-links a {
    color: #ffffff;
    opacity: 0.9;
    font-size: 0.9rem;
}

.legal-links a:hover {
    color: var(--secondary-color);
}

/* --- Responsive Styles --- */
@media (max-width: 1024px) {
    .container {
        padding: 0 3rem;
    }
    
    .hero h1 {
        font-size: 3rem;
    }
    
    body {
        padding-top: 80px;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 15px;
    }
    
    .container {
        padding: 0 2rem;
    }
    
    .section-padding {
        padding: 4rem 0;
    }
    
    .hero-section {
        height: 70vh;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .about-content,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .about-image {
        order: -1;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-nav {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background-color: white;
        flex-direction: column;
        padding: 2rem;
        transition: left var(--transition-normal);
        box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    }
    
    .main-nav.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        width: 100%;
        gap: 1rem;
    }
    
    .nav-item {
        width: 100%;
    }
    
    .nav-link {
        display: block;
        padding: 1rem;
        width: 100%;
        border-bottom: 1px solid var(--border-color);
    }
    
    .header-cta {
        margin: 2rem 0 0;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .footer-bottom .container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .legal-links {
        justify-content: center;
    }
    
    body {
        padding-top: 80px;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 1.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .contact-form-container {
        padding: 1.5rem;
    }
    
    .testimonial-card {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    body {
        padding-top: 80px;
    }
}

/* --- Animation Styles --- */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 1s ease forwards;
}

.slide-in-up {
    animation: slideInUp 1s ease forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Team Images */
.team-image:nth-child(1) img {
    content: url('https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=1976');
}

.team-image:nth-child(3) img {
    content: url('https://images.unsplash.com/photo-1565884280295-98eb83e41c65?q=80&w=1974');
}

.team-image:nth-child(5) img {
    content: url('https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1974');
} 