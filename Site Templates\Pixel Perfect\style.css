/* Pixel Perfect Digital Marketing Agency - Modern Style */
:root {
  --primary-color: #6c63ff; /* Vibrant purple */
  --primary-dark: #5a52d9;
  --secondary-color: #2d3748; /* Dark blue-gray */
  --accent-color: #ff6b6b; /* Coral */
  --text-color: #4a5568;
  --text-light: #718096;
  --white: #ffffff;
  --light-bg: #f7fafc;
  --dark-bg: #1a202c;
  --gray-bg: #edf2f7;
  --border-radius: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  background-color: var(--white);
  font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  line-height: 1.3;
  color: var(--secondary-color);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--primary-color);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background-color: rgba(108, 99, 255, 0.1);
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-header p {
  max-width: 600px;
  margin: 15px auto 0;
  color: var(--text-light);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 14px 30px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: 'Montserrat', sans-serif;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-secondary {
  background-color: transparent;
  color: var(--secondary-color);
  border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
  background-color: var(--secondary-color);
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

/* Header */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 20px 0;
  transition: var(--transition);
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-text {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1.8rem;
  color: var(--secondary-color);
}

.main-nav ul {
  display: flex;
  list-style: none;
}

.main-nav li {
  margin: 0 15px;
}

.main-nav a {
  color: var(--secondary-color);
  font-weight: 500;
  font-size: 1.05rem;
  position: relative;
  padding: 5px 0;
}

.main-nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav a:hover::after,
.main-nav a.active::after {
  width: 100%;
}

.main-nav a.active {
  color: var(--primary-color);
}

.nav-cta {
  background-color: var(--primary-color);
  color: var(--white) !important;
  padding: 10px 20px !important;
  border-radius: var(--border-radius);
  margin-left: 15px;
}

.nav-cta:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.nav-cta::after {
  display: none;
}

.mobile-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 24px;
  position: relative;
  z-index: 1001;
}

.mobile-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--secondary-color);
  position: absolute;
  left: 0;
  transition: var(--transition);
}

.mobile-toggle span:nth-child(1) {
  top: 0;
}

.mobile-toggle span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-toggle span:nth-child(3) {
  bottom: 0;
}

.mobile-toggle.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 11px;
}

.mobile-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-toggle.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 11px;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding-top: 80px;
}

.hero-content {
  max-width: 700px;
  position: relative;
  z-index: 1;
}

.hero h1 {
  margin-bottom: 20px;
  font-size: 4rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 40px;
  max-width: 600px;
}

.hero-cta {
  display: flex;
  gap: 20px;
}

.hero-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80');
  background-size: cover;
  background-position: center;
  z-index: 0;
  clip-path: polygon(100px 0, 100% 0, 100% 100%, 0 100%);
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
  z-index: 0;
}

/* Services Section */
.services {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.service-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 40px 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  width: 70px;
  height: 70px;
  background-color: rgba(108, 99, 255, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  font-size: 28px;
  transition: var(--transition);
}

.service-card:hover .service-icon {
  background-color: var(--primary-color);
  color: var(--white);
}

.service-card h3 {
  margin-bottom: 15px;
}

.service-card p {
  color: var(--text-light);
  margin-bottom: 25px;
}

.service-link {
  color: var(--primary-color);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.service-link i {
  margin-left: 5px;
  transition: var(--transition);
}

.service-link:hover i {
  transform: translateX(5px);
}

/* Work Section */
.work {
  padding: 100px 0;
}

.work-filters {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  background: none;
  border: none;
  padding: 8px 20px;
  margin: 0 5px 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  border-radius: 20px;
}

.filter-btn.active,
.filter-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.work-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.work-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  height: 300px;
}

.work-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.work-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  display: flex;
  align-items: flex-end;
  padding: 30px;
  opacity: 0;
  transition: var(--transition);
}

.work-item:hover .work-overlay {
  opacity: 1;
}

.work-item:hover img {
  transform: scale(1.1);
}

.work-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 0.8rem;
  padding: 5px 10px;
  border-radius: 3px;
  margin-bottom: 10px;
}

.work-info h3 {
  color: var(--white);
  margin-bottom: 15px;
}

.work-link {
  color: var(--white);
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: underline;
}

.work-cta {
  text-align: center;
}

/* Process Section */
.process {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.process-steps {
  max-width: 800px;
  margin: 0 auto;
}

.process-step {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-step::after {
  content: '';
  position: absolute;
  top: 70px;
  left: 30px;
  width: 1px;
  height: calc(100% - 30px);
  background-color: #e2e8f0;
  z-index: 0;
}

.process-step:last-child::after {
  display: none;
}

.step-number {
  flex: 0 0 60px;
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1.2rem;
  margin-right: 30px;
  position: relative;
  z-index: 1;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  margin-bottom: 10px;
}

.step-content p {
  color: var(--text-light);
  margin-bottom: 0;
}

/* About Section */
.about {
  padding: 100px 0;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-text {
  flex: 1;
}

.about-text p {
  margin-bottom: 25px;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.about-stats {
  display: flex;
  margin-top: 40px;
}

.stat {
  flex: 1;
  text-align: center;
  padding: 0 15px;
  border-right: 1px solid #e2e8f0;
}

.stat:last-child {
  border-right: none;
}

.stat-number {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Contact Section */
.contact {
  padding: 100px 0;
  background-color: var(--light-bg);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.contact-info h2 {
  margin-bottom: 20px;
}

.contact-info > p {
  margin-bottom: 40px;
}

.contact-details {
  margin-bottom: 40px;
}

.contact-item {
  display: flex;
  margin-bottom: 25px;
}

.contact-item i {
  flex: 0 0 40px;
  height: 40px;
  background-color: rgba(108, 99, 255, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.contact-item h3 {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.contact-item p {
  color: var(--text-light);
  margin-bottom: 0;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--white);
  color: var(--primary-color);
  border-radius: 50%;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.social-links a:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-3px);
}

.contact-form {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
}

/* Footer */
.site-footer {
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 80px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-logo .logo-text {
  color: var(--white);
  margin-bottom: 15px;
  display: inline-block;
}

.footer-logo p {
  opacity: 0.7;
  margin-bottom: 0;
}

.footer-links h3,
.footer-services h3,
.footer-newsletter h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after,
.footer-services h3::after,
.footer-newsletter h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul,
.footer-services ul {
  list-style: none;
}

.footer-links li,
.footer-services li {
  margin-bottom: 10px;
}

.footer-links a,
.footer-services a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover,
.footer-services a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-newsletter p {
  opacity: 0.7;
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  font-family: inherit;
  font-size: 0.95rem;
}

.newsletter-form button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0 20px;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background-color: var(--primary-dark);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  opacity: 0.7;
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  .hero h1 {
    font-size: 3.5rem;
  }
  
  .hero-bg {
    width: 40%;
  }
  
  .hero::after {
    width: 40%;
  }
  
  .about-content,
  .contact-content {
    gap: 40px;
  }
}

@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .hero h1 {
    font-size: 2.8rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .mobile-toggle {
    display: block;
  }
  
  .main-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background-color: var(--white);
    flex-direction: column;
    padding: 100px 40px 40px;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 999;
  }
  
  .main-nav.active {
    right: 0;
  }
  
  .main-nav ul {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-nav li {
    margin: 15px 0;
    width: 100%;
  }
  
  .nav-cta {
    margin-left: 0;
    margin-top: 15px;
    display: inline-block;
  }
  
  .hero {
    height: auto;
    padding: 150px 0 100px;
  }
  
  .hero-bg {
    display: none;
  }
  
  .hero::after {
    display: none;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: 15px;
  }
  
  .hero-cta .btn {
    width: 100%;
  }
  
  .about-content {
    flex-direction: column-reverse;
  }
  
  .about-image {
    flex: 0 0 100%;
    margin-bottom: 40px;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .footer-links h3::after,
  .footer-services h3::after,
  .footer-newsletter h3::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .newsletter-form {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .section-header {
    margin-bottom: 40px;
  }
  
  .services,
  .work,
  .process,
  .about,
  .contact {
    padding: 60px 0;
  }
  
  .process-step {
    flex-direction: column;
  }
  
  .step-number {
    margin-bottom: 15px;
  }
  
  .process-step::after {
    display: none;
  }
  
  .about-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .stat {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 20px;
  }
  
  .stat:last-child {
    border-bottom: none;
  }
  
  .contact-form {
    padding: 30px 20px;
  }
}
