/* <PERSON><PERSON>ault - Premium Record Store Styles */
:root {
  --primary-color: #e11d48; /* Rose/Red */
  --primary-dark: #be123c;
  --primary-light: #fb7185;
  --secondary-color: #0f172a; /* Dark blue/slate */
  --secondary-light: #1e293b;
  --accent-color: #f59e0b; /* Amber */
  --text-color: #0f172a;
  --text-light: #64748b;
  --white: #ffffff;
  --off-white: #f8fafc;
  --light-bg: #f1f5f9;
  --dark-bg: #0f172a;
  --darker-bg: #020617;
  --border-radius: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  background-color: var(--white);
  font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Oswald', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  text-transform: uppercase;
  letter-spacing: 1px;
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--primary-color);
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 40px;
  text-align: center;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: 'Oswald', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--text-color);
}

.btn-outline:hover {
  background-color: var(--text-color);
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

/* Header */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-family: 'Oswald', sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--text-color);
  text-transform: uppercase;
  letter-spacing: 2px;
}

.logo i {
  color: var(--primary-color);
  margin-right: 10px;
  font-size: 1.8rem;
}

.main-nav ul {
  display: flex;
  list-style: none;
}

.main-nav li {
  margin: 0 15px;
}

.main-nav a {
  color: var(--text-color);
  font-weight: 500;
  position: relative;
  padding: 5px 0;
  font-family: 'Oswald', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 1rem;
}

.main-nav a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav a:hover::after {
  width: 100%;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 24px;
  position: relative;
  z-index: 1001;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--text-color);
  position: absolute;
  left: 0;
  transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
  top: 0;
}

.mobile-menu-toggle span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
  bottom: 0;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 11px;
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 11px;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  min-height: 700px;
  background-image: url('https://images.unsplash.com/photo-1483412033650-1015ddeb83d1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  position: relative;
  color: var(--white);
}

.hero-section .container {
  position: relative;
  z-index: 1;
}

.hero-content {
  max-width: 600px;
}

.hero-section h1 {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  color: var(--white);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.7));
}

/* About Section */
.about-section {
  padding: 100px 0;
  background-color: var(--white);
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.about-text {
  flex: 1;
}

.about-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.feature {
  text-align: center;
}

.feature i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.feature h3 {
  margin-bottom: 10px;
}

.feature p {
  color: var(--text-light);
  margin-bottom: 0;
  font-size: 0.95rem;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Featured Section */
.featured-section {
  padding: 100px 0;
  background-color: var(--light-bg);
  text-align: center;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.record-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.record-card:hover {
  transform: translateY(-10px);
}

.record-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.record-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.record-card:hover .record-image img {
  transform: scale(1.1);
}

.record-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.record-card:hover .record-overlay {
  opacity: 1;
}

.record-info {
  padding: 20px;
  text-align: left;
}

.genre {
  display: inline-block;
  background-color: var(--light-bg);
  color: var(--text-color);
  font-size: 0.8rem;
  padding: 4px 10px;
  border-radius: 20px;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.record-info h3 {
  margin-bottom: 5px;
}

.record-info p {
  color: var(--text-light);
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.record-price {
  font-weight: 600;
  color: var(--primary-color);
}

/* Collection Section */
.collection-section {
  padding: 100px 0;
  background-color: var(--white);
  text-align: center;
}

.genre-tabs {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.genre-tab {
  background: none;
  border: none;
  padding: 8px 20px;
  margin: 0 5px 10px;
  font-family: 'Oswald', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.genre-tab.active,
.genre-tab:hover {
  color: var(--primary-color);
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.collection-category {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 30px;
  text-align: left;
  transition: var(--transition);
}

.collection-category:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow);
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.category-header i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-right: 15px;
}

.collection-category p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.collection-highlights {
  list-style: none;
  margin-bottom: 20px;
}

.collection-highlights li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 10px;
  color: var(--text-color);
}

.collection-highlights li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
}

/* Events Section */
.events-section {
  padding: 100px 0;
  background-color: var(--light-bg);
  text-align: center;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.event-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  display: flex;
}

.event-date {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.event-date .day {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.event-date .month {
  font-size: 1rem;
  font-weight: 600;
}

.event-content {
  padding: 25px;
  flex: 1;
  text-align: left;
}

.event-content h3 {
  margin-bottom: 10px;
}

.event-content p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.event-details span {
  font-size: 0.9rem;
  color: var(--text-light);
}

.event-details i {
  color: var(--primary-color);
  margin-right: 5px;
  width: 20px;
  text-align: center;
}

/* Locations Section */
.locations-section {
  padding: 100px 0;
  background-image: url('https://images.unsplash.com/photo-1520690214124-2405c5217036?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  color: var(--white);
  text-align: center;
}

.locations-section .container {
  position: relative;
  z-index: 1;
}

.locations-section h2,
.locations-section .section-subtitle {
  color: var(--white);
}

.locations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.location-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
  position: relative;
}

.location-card:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.15);
}

.featured-location {
  border: 2px solid var(--primary-color);
}

.featured-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 0.8rem;
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: 600;
}

.location-card h3 {
  margin-bottom: 20px;
  color: var(--white);
}

.location-details {
  margin-bottom: 30px;
}

.location-details p {
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.location-hours {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7) !important;
}

.location-feature {
  color: var(--primary-light) !important;
  font-weight: 600;
  margin-top: 10px;
}

.locations-section .btn-outline {
  color: var(--white);
  border-color: var(--white);
}

.locations-section .btn-outline:hover {
  background-color: var(--white);
  color: var(--text-color);
}

.locations-section .overlay {
  background: linear-gradient(to right, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.8));
}

/* Newsletter Section */
.newsletter-section {
  padding: 80px 0;
  background-color: var(--secondary-color);
  color: var(--white);
}

.newsletter-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.newsletter-content h2 {
  color: var(--white);
  margin-bottom: 20px;
}

.newsletter-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form input {
  flex: 1;
  padding: 15px;
  border: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
}

.newsletter-form .btn {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Footer */
.site-footer {
  background-color: var(--darker-bg);
  color: var(--white);
  padding: 80px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 40px;
  margin-bottom: 60px;
}

.footer-info {
  padding-right: 20px;
}

.footer-info .logo {
  color: var(--white);
  margin-bottom: 15px;
}

.footer-info p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-radius: 50%;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links h3,
.footer-contact h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after,
.footer-contact h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-contact p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 15px;
}

.footer-contact i {
  color: var(--primary-color);
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
  display: flex;
  gap: 20px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
}

.footer-legal a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  h1 {
    font-size: 3rem;
  }
  
  h2 {
    font-size: 2.2rem;
  }
  
  .hero-section h1 {
    font-size: 3.5rem;
  }
  
  .about-content {
    gap: 40px;
  }
  
  .about-features {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .hero-section h1 {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .main-nav {
    display: none;
  }
  
  .main-nav.active {
    display: block;
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .main-nav.active ul {
    flex-direction: column;
  }
  
  .main-nav.active li {
    margin: 10px 0;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .about-content {
    flex-direction: column;
  }
  
  .about-features {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .about-image {
    order: -1;
    margin-bottom: 30px;
  }
  
  .event-card {
    flex-direction: column;
  }
  
  .event-date {
    flex-direction: row;
    padding: 15px;
    justify-content: center;
  }
  
  .event-date .day {
    margin-right: 5px;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-form input {
    border-radius: var(--border-radius);
    margin-bottom: 10px;
  }
  
  .newsletter-form .btn {
    border-radius: var(--border-radius);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-links h3::after,
  .footer-contact h3::after {
    left: 0;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
  }
  
  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .section-subtitle {
    font-size: 1rem;
  }
  
  .about-section,
  .featured-section,
  .collection-section,
  .events-section,
  .locations-section,
  .newsletter-section {
    padding: 60px 0;
  }
}
