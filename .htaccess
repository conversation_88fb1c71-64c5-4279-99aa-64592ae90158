# Redirect common variations to canonical URLs
RewriteEngine On

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect www to non-www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Redirect index.html to root
RewriteCond %{THE_REQUEST} /index\.html[\s?] [NC]
RewriteRule ^index\.html$ / [R=301,L]

# Block direct access to template files
RewriteCond %{REQUEST_URI} ^/Site\ Templates/.*\.html$ [NC]
RewriteRule .* - [R=404,L]

# Custom 404 handling
ErrorDocument 404 /index.html

# Cache control for better performance
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
