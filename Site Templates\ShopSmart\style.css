@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme (Default) */
    --primary-l: #FFFFFF;
    --secondary-l: #F9FAFB;
    --text-l: #1F2937;
    --text-secondary-l: #6B7280;
    --accent-l: #3B82F6;
    --hover-accent-l: #2563EB;
    --border-l: #E5E7EB;
    --shadow-l: rgba(0, 0, 0, 0.05);

    /* Dark Theme */
    --primary-d: #111827; /* Dark Gray Blue */
    --secondary-d: #1F2937; /* Darker Gray Blue */
    --text-d: #F9FAFB; /* Light Gray */
    --text-secondary-d: #9CA3AF; /* Medium Gray */
    --accent-d: #60A5FA; /* Lighter Blue */
    --hover-accent-d: #93C5FD; /* Even Lighter Blue */
    --border-d: #374151; /* Gray */
    --shadow-d: rgba(255, 255, 255, 0.05); /* Subtle white shadow */

    /* Semantic Variables (Mapped) */
    --primary-color: var(--primary-l);
    --secondary-color: var(--secondary-l);
    --text-color: var(--text-l);
    --text-secondary-color: var(--text-secondary-l);
    --accent-color: var(--accent-l);
    --hover-accent-color: var(--hover-accent-l);
    --border-color: var(--border-l);
    --shadow-color: var(--shadow-l);

    /* Add transition for smooth theme change */
    --theme-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Apply Dark Theme */
body.dark-theme {
    --primary-color: var(--primary-d);
    --secondary-color: var(--secondary-d);
    --text-color: var(--text-d);
    --text-secondary-color: var(--text-secondary-d);
    --accent-color: var(--accent-d);
    --hover-accent-color: var(--hover-accent-d);
    --border-color: var(--border-d);
    --shadow-color: var(--shadow-d);
}

body {
    font-family: 'Inter', sans-serif; /* Modern font */
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--primary-color);
    transition: var(--theme-transition);
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.2s ease, var(--theme-transition);
}

a:hover {
    color: var(--hover-accent-color);
    text-decoration: none; /* Remove underline on hover for cleaner look */
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Container for consistent padding */
.container {
    max-width: 1280px; /* Slightly wider max-width */
    margin: 0 auto;
    padding: 0 2rem; /* Consistent horizontal padding */
}

/* Header & Navigation */
header {
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px var(--shadow-color);
    padding: 0.75rem 0; /* Slightly less padding */
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    transition: var(--theme-transition);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap; /* Allow wrapping on small screens */
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative; /* For absolute positioning of dropdown */
}

nav .logo {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-color);
    transition: var(--theme-transition);
    z-index: 101; /* Keep above mobile menu */
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none; /* Hidden on desktop */
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 101; /* Keep above mobile menu */
}

.mobile-menu-toggle .bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: var(--text-color);
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Navigation Container */
.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(100% - 200px); /* Adjust based on logo width */
}

/* Navigation Links */
.nav-links {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.nav-links li {
    position: relative; /* For dropdown positioning */
}

.nav-links a {
    font-weight: 500;
    font-size: 1rem;
    color: var(--text-secondary-color);
    transition: color 0.2s ease, var(--theme-transition);
    display: block;
    padding: 0.5rem 0;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--accent-color);
}

/* Dropdown Menu */
.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.dropdown-icon {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-icon {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px var(--shadow-color);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
    z-index: 100;
    padding: 0.5rem 0;
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: 0.5rem 1rem;
    font-size: 0.95rem;
    display: block;
    color: var(--text-secondary-color);
    transition: background-color 0.2s ease, color 0.2s ease;
}

.dropdown-menu a:hover {
    background-color: var(--secondary-color);
    color: var(--accent-color);
}

/* Right Side Navigation */
.nav-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Search Bar */
.search-bar {
    display: flex;
    position: relative;
}

.search-bar input {
    padding: 0.6rem 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 6px 0 0 6px;
    font-size: 0.9rem;
    min-width: 200px;
    border-right: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.search-bar button {
    padding: 0.6rem 1rem;
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.search-bar button:hover {
    background-color: var(--hover-accent-color);
}

/* Navigation Icons */
.nav-icons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.wishlist-icon,
.cart-icon {
    position: relative;
    color: var(--text-secondary-color);
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wishlist-icon:hover,
.cart-icon:hover {
    color: var(--accent-color);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--accent-color);
    color: var(--primary-color);
    font-size: 0.7rem;
    font-weight: 700;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Navigation */
@media (max-width: 992px) {
    .nav-container {
        width: auto; /* Adjust for medium screens */
    }

    .search-bar input {
        min-width: 150px; /* Smaller search on medium screens */
    }
}

@media (max-width: 768px) {
    nav {
        padding: 0 1rem;
    }

    .mobile-menu-toggle {
        display: block; /* Show on mobile */
    }

    .nav-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: var(--primary-color);
        flex-direction: column;
        justify-content: flex-start;
        padding: 5rem 2rem 2rem;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 100;
        overflow-y: auto;
    }

    body.menu-open .nav-container {
        transform: translateX(0);
    }

    /* Animate hamburger to X */
    body.menu-open .mobile-menu-toggle .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    body.menu-open .mobile-menu-toggle .bar:nth-child(2) {
        opacity: 0;
    }

    body.menu-open .mobile-menu-toggle .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .nav-links {
        flex-direction: column;
        width: 100%;
        margin-bottom: 2rem;
    }

    .nav-links a {
        padding: 0.75rem 0;
        font-size: 1.1rem;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background-color: transparent;
        padding-left: 1rem;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .dropdown.open .dropdown-menu {
        max-height: 500px; /* Arbitrary large value */
    }

    .dropdown-toggle {
        justify-content: space-between;
    }

    .nav-right {
        flex-direction: column;
        width: 100%;
        gap: 1rem;
        align-items: flex-start;
    }

    .search-bar {
        width: 100%;
    }

    .search-bar input {
        width: 100%;
        min-width: 0;
    }

    .nav-icons {
        align-self: center;
        margin-top: 1rem;
    }
}

/* Main Content Area */
main {
    padding: 3rem 0; /* Apply vertical padding to main */
}

/* Use container within sections for consistent padding */
main section {
     margin-bottom: 3rem; /* Consistent spacing between sections */
}

main section .container {
     padding-top: 2rem;
     padding-bottom: 2rem;
}

/* Hero Section with Carousel */
.hero {
    position: relative;
    margin-bottom: 3rem; /* Spacing after hero */
    height: 500px; /* Fixed height for hero */
    overflow: hidden;
}

.hero-carousel {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.8s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
    z-index: 1;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 2;
}

.carousel-content {
    position: relative;
    z-index: 3;
    text-align: center;
    max-width: 800px;
    padding: 0 2rem;
    color: white; /* Always use white text for better contrast on dark image backgrounds */
}

.carousel-content h1 {
    font-size: 3.5rem; /* Larger heading */
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.carousel-content p {
    font-size: 1.25rem; /* Slightly larger text */
    margin-bottom: 2rem;
    max-width: 600px; /* Limit width */
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.7);
    color: var(--text-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease, background-color 0.3s ease;
}

.carousel-control:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.9);
}

.carousel-control.prev {
    left: 20px;
}

.carousel-control.next {
    right: 20px;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.indicator.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Dark theme adjustments for carousel */
body.dark-theme .carousel-control {
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--text-d);
}

body.dark-theme .carousel-control:hover {
    background-color: rgba(0, 0, 0, 0.9);
}

.cta-button {
    display: inline-block;
    padding: 0.9rem 2rem; /* Larger padding */
    background-color: var(--accent-color);
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 500; /* Medium weight */
    border-radius: 6px; /* Consistent rounding */
    transition: background-color 0.2s ease, transform 0.2s ease;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.cta-button:hover {
    background-color: var(--hover-accent-color);
    transform: translateY(-2px); /* Subtle lift effect */
}

/* Section Titles */
h2.section-title {
    text-align: center;
    margin-bottom: 2.5rem; /* More space below title */
    font-size: 2.25rem; /* Larger section titles */
    font-weight: 700;
}

/* Product Grid */
.product-grid {
    margin-bottom: 3rem;
}

.filter-options {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-secondary-color);
}

.filter-options select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-left: 0.5rem;
}

.products {
    display: grid;
    /* Adjust minmax for slightly larger cards */
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem; /* Increased gap */
}

/* Product Cards */
.product-card {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--primary-color);
    box-shadow: 0 4px 12px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative; /* For positioning badges and actions */
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

/* Product Image Container */
.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 280px; /* Slightly taller */
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05); /* Subtle zoom effect */
}

/* Product Badge (Sale, New, etc.) */
.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 4px;
    z-index: 2;
}

/* Product Action Buttons */
.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 2;
    opacity: 0;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-view-btn,
.wishlist-btn {
    background-color: var(--primary-color);
    color: var(--text-secondary-color);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
}

.quick-view-btn:hover,
.wishlist-btn:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Product Info */
.product-info {
    padding: 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-info a {
    text-decoration: none;
    color: inherit;
}

.product-info h3 {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 0 0.75rem;
    color: var(--text-color);
    transition: color 0.2s ease;
}

.product-info a:hover h3 {
    color: var(--accent-color);
}

/* Product Meta (Price and Rating) */
.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto; /* Push to bottom of info section */
}

.price {
    font-weight: 700;
    color: var(--text-color);
    font-size: 1.1rem;
}

.original-price {
    text-decoration: line-through;
    color: var(--text-secondary-color);
    font-weight: 400;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

/* Star Ratings */
.product-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stars {
    color: #F59E0B; /* Amber color for stars */
    letter-spacing: -1px; /* Tighten star spacing */
}

.empty-star {
    color: var(--border-color);
}

.rating-count {
    font-size: 0.8rem;
    color: var(--text-secondary-color);
}

/* Add to Cart Button */
.add-to-cart-button {
    display: block;
    width: 100%;
    padding: 0.8rem;
    background-color: var(--secondary-color);
    color: var(--accent-color);
    border: none;
    border-top: 1px solid var(--border-color);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
    border-radius: 0 0 8px 8px;
    margin-top: auto;
}

.add-to-cart-button:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

/* Quick View Modal (will be implemented in JS) */
.quick-view-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.quick-view-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--primary-color);
    border-radius: 8px;
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.quick-view-modal.active .modal-content {
    transform: translateY(0);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1;
}

/* Responsive adjustments for product cards */
@media (max-width: 768px) {
    .product-image img {
        height: 220px; /* Smaller on mobile */
    }

    .product-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .product-rating {
        margin-left: 0;
    }
}

/* Newsletter */
.newsletter {
    background-color: var(--secondary-color);
    text-align: center;
    padding: 3rem 2rem; /* Increased padding */
    border-radius: 8px;
    margin-bottom: 3rem;
}

.newsletter h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.newsletter p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary-color);
}

.newsletter form {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

.newsletter form input {
    padding: 0.75rem 1rem; /* More padding */
    border: 1px solid var(--border-color);
    border-radius: 6px;
    width: 300px; /* Adjust as needed */
    margin-right: 0; /* Remove margin, use gap */
    font-size: 1rem;
}

.newsletter form input:focus {
     outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); /* Focus ring */
}

.newsletter form button {
    padding: 0.75rem 1.5rem;
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.newsletter form button:hover {
    background-color: var(--hover-accent-color);
}

/* Footer */
footer {
    background-color: var(--secondary-color);
    color: var(--text-secondary-color);
    padding: 3rem 0;
    margin-top: 3rem;
    border-top: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.footer-content {
    display: grid; /* Use grid for better alignment */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive columns */
    gap: 2rem;
    margin-bottom: 2rem;
    /* Use container */
    max-width: 1280px;
    margin: 0 auto 2rem auto;
    padding: 0 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 500;
    transition: var(--theme-transition);
}

.footer-section p {
    font-size: 0.95rem;
    line-height: 1.5;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary-color);
    font-size: 0.95rem;
    transition: color 0.2s ease, var(--theme-transition);
}

.footer-section ul li a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    color: var(--text-secondary-color);
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
}

.social-link:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.social-link svg {
    width: 20px;
    height: 20px;
}

.copyright {
    text-align: center;
    font-size: 0.9rem;
    margin-top: 2rem;
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
    color: var(--text-secondary-color);
    max-width: 1280px;
    margin: 2rem auto 0 auto;
    padding: 2rem 2rem 0 2rem;
    transition: var(--theme-transition);
}

/* Basic Responsive Adjustments */
@media (max-width: 992px) {
     nav ul {
         gap: 1rem; /* Reduce gap on medium screens */
     }
     .search-bar {
         /* Maybe hide search bar or move it below on smaller screens */
     }
}

@media (max-width: 768px) {
    /* Stack header items */
    header {
        padding: 1rem 0;
        position: static; /* Or adjust sticky behavior */
    }
    nav {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem; /* Space between logo, nav links, search */
        padding: 0 1rem; /* Adjust padding for smaller screens */
    }

    nav ul {
        margin-top: 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem; /* Smaller gap for vertical list */
        width: 100%; /* Full width */
    }

    nav ul li {
        margin-left: 0;
        margin-bottom: 0;
    }

    .search-bar {
        margin-top: 0;
        width: 100%;
    }

    .search-bar input {
         flex-grow: 1; /* Allow input to take available space */
         min-width: 0;
    }

    main {
        padding: 2rem 0; /* Adjust main padding */
    }

    main section .container,
    .hero .container,
    .footer-content,
    .copyright {
        padding: 0 1rem; /* Reduce horizontal padding */
    }

    .hero h1 {
        font-size: 2.5rem;
    }
    .hero p {
        font-size: 1.1rem;
    }
    h2.section-title {
        font-size: 2rem;
    }

    .products {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Adjust grid for smaller screens */
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr; /* Stack footer sections */
        text-align: center; /* Center text in stacked footer */
    }
     .footer-section ul {
         padding-left: 0; /* Remove indent */
     }

    .newsletter form {
        flex-direction: column;
        align-items: stretch; /* Make input/button full width */
    }
    .newsletter form input {
        width: 100%;
    }
}

/* Accessibility Focus */
*:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* ============================
   Shopping Cart Page Styles
   ============================ */

.cart-items {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden; /* Ensures rounded corners contain children */
    margin-bottom: 2rem;
}

.cart-header,
.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    gap: 1rem;
}

.cart-header {
    background-color: var(--secondary-color);
    color: var(--text-secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    text-transform: uppercase;
}

.cart-item {
    border-bottom: 1px solid var(--border-color);
}

.cart-item:last-child {
    border-bottom: none;
}

/* Define Column Widths */
.cart-col {
    display: flex;
    align-items: center; /* Vertically center content in column */
}

.cart-col.product {
    flex: 3; /* Takes up more space */
    gap: 1rem;
}

.cart-col.price,
.cart-col.total {
    flex: 1;
    justify-content: center;
    text-align: center;
}

.cart-col.quantity {
    flex: 1;
    justify-content: center;
}

.cart-col.remove {
    flex: 0 0 50px; /* Fixed width */
    justify-content: center;
}

.cart-col.product img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.cart-col.product .item-details {
    display: flex;
    flex-direction: column;
}

.cart-col.product .item-details a {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}
.cart-col.product .item-details a:hover {
    color: var(--accent-color);
}

.cart-col.product .item-details span {
    font-size: 0.85rem;
    color: var(--text-secondary-color);
}

.cart-col.quantity input {
    width: 60px;
    padding: 0.4rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
    font-size: 1rem;
}
.cart-col.quantity input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}


.remove-item-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary-color);
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

.remove-item-btn:hover {
    background-color: #FEE2E2; /* Light red */
    color: #DC2626; /* Red */
    border-color: #FEE2E2;
}

/* Cart Summary */
.cart-summary {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem 2rem;
    max-width: 400px; /* Limit width */
    margin-left: auto; /* Align to the right */
}

.cart-summary h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: left;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    font-size: 1rem;
    color: var(--text-secondary-color);
}

.summary-row.total-row {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--text-color);
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 0.5rem;
}

.checkout-button {
    width: 100%;
    margin-top: 1rem;
    padding: 0.9rem 1.5rem; /* Match cta-button */
}

/* Cart Responsive */
@media (max-width: 768px) {
    .cart-header {
        display: none; /* Hide header on small screens */
    }

    .cart-item {
        flex-direction: column; /* Stack items vertically */
        align-items: flex-start; /* Align items left */
        padding: 1rem;
    }

    .cart-col {
        width: 100%; /* Make columns full width */
        justify-content: space-between; /* Space out content within column */
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px dashed var(--border-color);
    }
    .cart-item .cart-col:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    /* Re-introduce labels for context */
    .cart-col.price::before,
    .cart-col.quantity::before,
    .cart-col.total::before {
        content: attr(class);
        display: inline-block;
        font-weight: 500;
        width: 80px; /* Align labels */
        color: var(--text-secondary-color);
        text-transform: capitalize;
    }
     .cart-col.quantity::before {
        content: 'Quantity:';
     }
    .cart-col.price::before {
        content: 'Price:';
     }
    .cart-col.total::before {
        content: 'Total:';
     }

    .cart-col.product {
        border-bottom: none; /* Remove border for product block */
        margin-bottom: 0.5rem;
        padding-bottom: 0;
        gap: 0.5rem;
    }
    .cart-col.product img {
        width: 60px; /* Smaller image */
        height: 60px;
    }

    .cart-col.remove {
        justify-content: flex-end; /* Move button to the right */
        border-bottom: none;
        padding-bottom: 0;
    }

     .cart-summary {
        max-width: none; /* Full width on mobile */
        margin-left: 0;
     }
}

/* ===============================
   Product Detail Page Styles
   =============================== */

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: 3rem; /* Gap between columns */
    align-items: flex-start; /* Align items to the top */
    margin-bottom: 3rem;
}

/* Product Gallery */
.product-gallery {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

#main-product-image {
    width: 100%;
    max-width: 600px; /* Limit max size */
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow-color) 0px 4px 12px;
}

.thumbnails {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap; /* Allow wrapping if many thumbs */
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease, border-color 0.2s ease;
}

.thumbnail:hover,
.thumbnail.active {
    opacity: 1;
    border-color: var(--accent-color);
}

/* Product Info Section */
.product-info {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* Space between elements */
}

.product-title {
    font-size: 2.5rem; /* Larger title */
    font-weight: 700;
    margin-bottom: 0.5rem; /* Less space below title */
}

.product-price {
    font-size: 1.75rem; /* Larger price */
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.product-description {
    color: var(--text-secondary-color);
    line-height: 1.7;
}

.product-options {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.option-group label {
    font-weight: 500;
    width: 80px; /* Align labels */
}

.option-group select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    flex-grow: 1; /* Allow select to take space */
    max-width: 200px;
}
.option-group select:focus {
     outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}


.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
}

.quantity-selector label {
    font-weight: 500;
    width: 80px; /* Align labels */
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.quantity-btn {
    width: 36px;
    height: 36px;
    background-color: var(--secondary-color);
    border: none;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.quantity-btn:hover {
    background-color: var(--border-color);
}

.quantity-selector input {
    width: 50px;
    height: 36px;
    border: none;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    text-align: center;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    background-color: var(--primary-color);
}

.quantity-selector input:focus {
    outline: none;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.add-to-cart-button-detail {
    flex: 1;
    padding: 0.9rem 1.5rem;
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.add-to-cart-button-detail:hover {
    background-color: var(--hover-accent-color);
    transform: translateY(-2px);
}

.view-details-button {
    padding: 0.9rem 1.5rem;
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.view-details-button:hover {
    background-color: var(--border-color);
    color: var(--accent-color);
}

/* Quick View Product Detail Styles */
.quick-view-product {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.quick-view-product .product-gallery img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.quick-view-product .product-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

.quick-view-product .product-price {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.quick-view-product .product-description {
    margin: 1rem 0;
    line-height: 1.6;
    color: var(--text-secondary-color);
}

@media (max-width: 768px) {
    .quick-view-product {
        grid-template-columns: 1fr;
        padding: 1.5rem;
    }
}


.add-to-cart-button-detail {
    margin-top: 1.5rem;
    padding: 1rem 2rem; /* Make button larger */
    font-size: 1.1rem;
    width: 100%; /* Make button full width */
    max-width: 300px; /* Optional: Limit max width */
}

/* Customer Reviews */
.customer-reviews {
    margin-top: 4rem; /* More space above reviews */
    border-top: 1px solid var(--border-color);
    padding-top: 3rem;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.review {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    background-color: var(--secondary-color);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.reviewer-name {
    font-weight: 500;
}

.review-rating {
    color: #F59E0B; /* Amber color for stars */
}

.review-text {
    color: var(--text-secondary-color);
    font-size: 0.95rem;
}

/* Product Detail Responsive */
@media (max-width: 992px) {
    .product-detail {
        grid-template-columns: 1fr; /* Stack columns */
        gap: 2rem;
    }
    .product-gallery {
        max-width: 500px; /* Center gallery */
        margin: 0 auto;
    }
     .product-info {
         text-align: center;
     }
     .product-options,
     .quantity-selector {
         justify-content: center;
     }
     .option-group label,
     .quantity-selector label {
         width: auto; /* Remove fixed width */
     }
     .add-to-cart-button-detail {
         margin-left: auto;
         margin-right: auto;
     }

}

/* --- Theme Switch Styles --- */
.theme-switch {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary-color);
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--theme-transition), transform 0.2s ease;
}

/* Hide moon icon by default (light theme) */
.theme-switch .moon-icon {
    display: none;
}

/* Show sun icon by default */
.theme-switch .sun-icon {
    display: block;
}

/* When dark theme is active, hide sun and show moon */
body.dark-theme .theme-switch .sun-icon {
    display: none;
}
body.dark-theme .theme-switch .moon-icon {
    display: block;
}

.theme-switch:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
    transform: rotate(15deg);
}

.theme-switch svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* Product Detail Page */
.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: 2rem; /* Space between columns */
    margin-top: 2rem;
}

/* Product Gallery */
.product-gallery {
    position: relative;
}

.main-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.product-gallery img#main-product-image {
    width: 100%;
    height: auto;
    transition: transform 0.5s ease;
}

.main-image-container:hover img#main-product-image {
    transform: scale(1.05);
}

.product-wishlist {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: var(--text-secondary-color);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
    z-index: 2;
}

.product-wishlist:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

.product-wishlist.active {
    color: #ef4444; /* Red heart when active */
}

.thumbnails {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.2s ease, transform 0.2s ease;
}

.thumbnail:hover {
    transform: translateY(-2px);
}

.thumbnail.active {
    border-color: var(--accent-color);
}

/* Product Info */
.product-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

.product-badges {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge.in-stock {
    background-color: #10B981; /* Green */
    color: white;
}

.badge.free-shipping {
    background-color: #6366F1; /* Indigo */
    color: white;
}

.product-description {
    margin-bottom: 1.5rem;
    line-height: 1.6;
    color: var(--text-secondary-color);
}

/* Product Options */
.product-options {
    margin-bottom: 1.5rem;
}

.option-group {
    margin-bottom: 1.25rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: var(--text-color);
}

.color-options {
    display: flex;
    gap: 0.75rem;
}

.color-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: transform 0.2s ease, border-color 0.2s ease;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.active {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color);
}

.styled-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--primary-color);
    color: var(--text-color);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236B7280' width='18' height='18'%3E%3Cpath fill-rule='evenodd' d='M12.53 16.28a.75.75 0 01-1.06 0l-6-6a.75.75 0 011.06-1.06L12 14.69l5.47-5.47a.75.75 0 111.06 1.06l-6 6z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.styled-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.add-to-cart-button-detail {
    flex: 1;
    padding: 1rem 1.5rem;
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-to-cart-button-detail:hover {
    background-color: var(--hover-accent-color);
    transform: translateY(-2px);
}

.buy-now-button {
    padding: 1rem 1.5rem;
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.buy-now-button:hover {
    background-color: var(--border-color);
    color: var(--accent-color);
}

/* Additional Info */
.additional-info {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary-color);
}

.info-item svg {
    color: var(--accent-color);
    flex-shrink: 0;
}

/* Customer Reviews Section */
.customer-reviews {
    margin-top: 3rem;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.review {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--shadow-color);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.reviewer-name {
    font-weight: 600;
    color: var(--text-color);
}

.review-rating {
    color: #F59E0B; /* Amber color for stars */
}

.review-text {
    line-height: 1.6;
    color: var(--text-secondary-color);
}

/* Responsive Adjustments for Product Detail */
@media (max-width: 992px) {
    .product-detail {
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .product-detail {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
    }

    .buy-now-button {
        width: 100%;
    }
}

/* Cart Page Improvements */

/* Quantity Controls in Cart */
.cart-col.quantity .quantity-controls {
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.cart-col.quantity .quantity-btn {
    width: 30px;
    height: 30px;
    background-color: var(--secondary-color);
    border: none;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.cart-col.quantity .quantity-btn:hover {
    background-color: var(--border-color);
}

.cart-col.quantity .quantity-controls input {
    width: 40px;
    height: 30px;
    border: none;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    background-color: var(--primary-color);
    padding: 0;
    margin: 0;
}

/* Improved Remove Button */
.remove-item-btn svg {
    width: 20px;
    height: 20px;
}

/* Promo Code */
.promo-code {
    margin: 1.5rem 0;
}

.promo-code label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.promo-input-group {
    display: flex;
}

.promo-input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: 4px 0 0 4px;
    font-size: 0.9rem;
    background-color: var(--primary-color);
    color: var(--text-color);
}

.promo-input-group input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.apply-promo-btn {
    padding: 0.75rem 1rem;
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0 4px 4px 0;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.apply-promo-btn:hover {
    background-color: var(--border-color);
    color: var(--accent-color);
}

/* Continue Shopping Link */
.continue-shopping {
    margin-top: 1.5rem;
    text-align: center;
}

.continue-shopping a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary-color);
    font-size: 0.9rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.continue-shopping a:hover {
    color: var(--accent-color);
}

.continue-shopping svg {
    width: 16px;
    height: 16px;
}

/* Discount Row */
.discount-row {
    color: #10B981; /* Green for discount */
}
