
/* Base Styles and Custom Properties */
:root {
    --color-primary: #1e3a8a;
    --color-primary-light: #80deea;
    --color-primary-dark: #1e40af;
    --color-white: #ffffff;
    --color-black: #000000;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    --font-playfair: 'Playfair Display', serif;
    --font-montserrat: 'Montserrat', sans-serif;

    --transition-default: all 0.3s ease-in-out;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.03);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.07), 0 2px 4px -2px rgb(0 0 0 / 0.07);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
  }

  /* Global Reset and Base Styles */
  *, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-family: var(--font-montserrat);
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    min-height: 100vh;
    color: var(--color-gray-900);
    background-color: var(--color-white);
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-playfair);
    font-weight: 700;
    line-height: 1.2;
  }

  .highlight {
    color: var(--color-primary);
  }

  /* Use lighter highlight color for sections with dark backgrounds */
  .hero-content .highlight,
  .newsletter-section .highlight,
  .site-footer .logo-text,
  .site-footer .logo-icon {
    color: var(--color-primary-light);
  }

  /* Header Styles */
  .site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    box-shadow: var(--shadow-sm);
    padding: 1rem 0;
  }

  .container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .site-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--color-primary);
  }

  .logo-icon {
    font-size: 2rem;
  }

  .logo-text {
    font-family: var(--font-playfair);
    font-size: 1.5rem;
    font-weight: 700;
  }

  /* Navigation */
  .main-nav ul {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
  }

  .main-nav a:not(.btn) {
    color: var(--color-gray-700);
    text-decoration: none;
    transition: var(--transition-default);
  }

  .main-nav a:not(.btn):hover {
    color: var(--color-primary);
  }

  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 9999px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-default);
    position: relative;
    overflow: hidden;
    z-index: 1;
  }

  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-white);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .btn-primary:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }

  .btn-outline {
    border: 2px solid var(--color-white);
    color: var(--color-white);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .btn-outline:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }

  /* Special styling for buttons on light backgrounds */
  .room-price .btn-outline,
  .dining-content .btn-outline {
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
    background-color: transparent;
  }

  .room-price .btn-outline:hover,
  .dining-content .btn-outline:hover {
    background-color: rgba(30, 58, 138, 0.05);
    color: var(--color-primary-dark);
    transform: translateY(-3px);
  }

  .btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Mobile Menu */
  .mobile-menu-toggle {
    display: none;
    padding: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
  }

  .mobile-menu-toggle span {
    display: block;
    width: 24px;
    height: 2px;
    background-color: var(--color-gray-600);
    transition: var(--transition-default);
  }

  .mobile-menu-toggle span + span {
    margin-top: 5px;
  }

  .mobile-menu-toggle.active span:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
  }

  .mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.active span:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
  }

  @media (max-width: 768px) {
    .mobile-menu-toggle {
      display: block;
    }

    .main-nav {
      display: none;
    }

    .main-nav.active {
      display: block;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: var(--color-white);
      padding: 1rem;
      box-shadow: var(--shadow-md);
    }

    .main-nav.active ul {
      flex-direction: column;
      gap: 1rem;
    }
  }

  /* Hero Section */
  .hero-section {
    position: relative;
    height: 100vh;
    color: var(--color-white);
    background-image: url('https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
  }

  .overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.25);
  }

  .dining-section .overlay,
  .location-section .overlay {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .hero-content {
    position: relative;
    z-index: 1;
    max-width: 36rem;
    padding-top: 8rem;
  }

  .hero-content h1 {
    font-size: clamp(3rem, 8vw, 4.5rem);
    margin-bottom: 1rem;
  }

  .hero-content h2 {
    font-size: clamp(2rem, 6vw, 3rem);
    font-weight: 300;
    margin-bottom: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: var(--color-gray-200);
  }

  .hero-cta {
    display: flex;
    gap: 1rem;
  }

  /* About Section */
  .about-section {
    padding: 5rem 0;
    background-color: var(--color-white);
  }

  .about-content {
    display: grid;
    gap: 4rem;
    align-items: center;
  }

  @media (min-width: 768px) {
    .about-content {
      grid-template-columns: 1fr 1fr;
    }
  }

  .about-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .about-text p {
    color: var(--color-gray-600);
    margin-bottom: 1.5rem;
  }

  .awards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .award {
    text-align: center;
  }

  .award i {
    font-size: 2rem;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
  }

  .award span {
    font-size: 0.875rem;
    color: var(--color-gray-600);
  }

  .about-image {
    position: relative;
    height: 100%;
    min-height: 400px;
  }

  .about-image img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
  }

  /* Accommodations Section */
  .accommodations-section {
    padding: 5rem 0;
    background-color: var(--color-gray-50);
  }

  .room-slider {
    display: grid;
    gap: 2rem;
    margin-top: 3rem;
  }

  @media (min-width: 768px) {
    .room-slider {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .room-card {
    background-color: var(--color-white);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-default);
    position: relative;
  }

  .room-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }

  .room-image {
    position: relative;
    height: 250px;
  }

  .room-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .room-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-default);
  }

  .room-card:hover .room-overlay {
    opacity: 1;
  }

  .room-info {
    padding: 1.5rem;
  }

  .room-info h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .room-features {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    color: var(--color-gray-600);
    font-size: 0.875rem;
  }

  .room-features span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .room-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-gray-200);
  }

  .featured-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--color-primary);
    color: var(--color-white);
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 600;
    z-index: 10;
  }

  /* Experiences Section */
  .experiences-section {
    padding: 5rem 0;
    background-color: var(--color-white);
  }

  .experiences-grid {
    display: grid;
    gap: 2rem;
    margin-top: 3rem;
  }

  @media (min-width: 768px) {
    .experiences-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .experience-card {
    text-align: center;
    padding: 2rem;
    background-color: var(--color-gray-50);
    border-radius: 0.5rem;
    transition: var(--transition-default);
    text-decoration: none;
    color: inherit;
  }

  .experience-card:hover {
    transform: translateY(-4px);
    background-color: var(--color-white);
    box-shadow: var(--shadow-lg);
  }

  .experience-icon {
    font-size: 2.5rem;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .experience-card h3 {
    margin-bottom: 0.5rem;
  }

  .experience-card p {
    color: var(--color-gray-600);
    font-size: 0.875rem;
  }

  /* Dining Section */
  .dining-section {
    padding: 5rem 0;
    background-color: var(--color-gray-50);
    position: relative;
  }

  .dining-grid {
    display: grid;
    gap: 2rem;
    margin-top: 3rem;
  }

  @media (min-width: 768px) {
    .dining-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .dining-card {
    background-color: var(--color-white);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
  }

  .dining-image {
    height: 250px;
  }

  .dining-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .dining-content {
    padding: 1.5rem;
  }

  .dining-type {
    color: var(--color-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .dining-description {
    color: var(--color-gray-600);
    margin-bottom: 1rem;
  }

  .dining-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--color-gray-600);
  }

  .dining-details span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Spa Section */
  .spa-section {
    padding: 5rem 0;
    background-color: var(--color-white);
  }

  .spa-content {
    display: grid;
    gap: 4rem;
    align-items: center;
  }

  @media (min-width: 768px) {
    .spa-content {
      grid-template-columns: 1fr 1fr;
    }
  }

  .spa-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 2rem 0;
  }

  .spa-feature {
    text-align: center;
  }

  .spa-feature i {
    font-size: 2rem;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }

  .spa-feature h3 {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
  }

  .spa-feature p {
    font-size: 0.875rem;
    color: var(--color-gray-600);
  }

  /* Location Section */
  .location-section {
    padding: 5rem 0;
    background-color: var(--color-white);
    position: relative;
  }

  .location-content {
    display: grid;
    gap: 4rem;
    margin-top: 3rem;
  }

  @media (min-width: 768px) {
    .location-content {
      grid-template-columns: 1fr 1fr;
    }
  }

  .location-info {
    margin-bottom: 2rem;
  }

  .location-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .location-directions {
    margin-bottom: 2rem;
  }

  .direction {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .direction i {
    font-size: 1.5rem;
    color: var(--color-primary);
  }

  .location-attractions ul {
    list-style: none;
    margin-bottom: 2rem;
  }

  .location-attractions li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
  }

  .location-attractions li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--color-primary);
  }

  .location-map {
    height: 100%;
    min-height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .location-map img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Newsletter Section */
  .newsletter-section {
    padding: 5rem 0;
    background-color: var(--color-primary);
    color: var(--color-white);
  }

  .newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .newsletter-form {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }

  .newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 9999px;
    font-size: 1rem;
  }

  .newsletter-form input:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-light);
  }

  /* Footer */
  .site-footer {
    background-color: var(--color-gray-900);
    color: var(--color-white);
    padding: 4rem 0 2rem;
  }

  .footer-content {
    display: grid;
    gap: 2rem;
  }

  @media (min-width: 768px) {
    .footer-content {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .footer-info {
    margin-bottom: 2rem;
  }

  .footer-info p {
    color: var(--color-gray-400);
    margin: 1rem 0;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .social-links a {
    color: var(--color-gray-400);
    font-size: 1.25rem;
    transition: var(--transition-default);
  }

  .social-links a:hover {
    color: var(--color-white);
  }

  .footer-links h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .footer-links ul {
    list-style: none;
  }

  .footer-links li {
    margin-bottom: 0.5rem;
  }

  .footer-links a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: var(--transition-default);
  }

  .footer-links a:hover {
    color: var(--color-white);
  }

  .footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-gray-400);
    margin-bottom: 0.5rem;
  }

  .footer-bottom {
    margin-top: 4rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-gray-800);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--color-gray-400);
  }

  .footer-legal {
    display: flex;
    gap: 2rem;
  }

  .footer-legal a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: var(--transition-default);
  }

  .footer-legal a:hover {
    color: var(--color-white);
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  /* Utility Classes */
  .text-center { text-align: center; }
  .relative { position: relative; }
  .absolute { position: absolute; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
  .z-10 { z-index: 10; }
  .hidden { display: none; }
  .flex { display: flex; }
  .items-center { align-items: center; }
  .justify-between { justify-content: space-between; }
  .space-x-4 > * + * { margin-left: 1rem; }
  .space-y-2 > * + * { margin-top: 0.5rem; }
  .rounded-full { border-radius: 9999px; }
  .shadow-sm { box-shadow: var(--shadow-sm); }
  .shadow-md { box-shadow: var(--shadow-md); }
  .shadow-lg { box-shadow: var(--shadow-lg); }

  /* Responsive Utilities */
  @media (min-width: 640px) {
    .sm\:block { display: block; }
    .sm\:hidden { display: none; }
  }

  @media (min-width: 768px) {
    .md\:block { display: block; }
    .md\:hidden { display: none; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  }

  @media (min-width: 1024px) {
    .lg\:block { display: block; }
    .lg\:hidden { display: none; }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  }
